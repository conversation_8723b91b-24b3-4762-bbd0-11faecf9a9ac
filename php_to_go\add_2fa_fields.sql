-- 添加2FA相关字段到admin_users表
ALTER TABLE `admin_users` 
ADD COLUMN `two_factor_enabled` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用双因素认证' AFTER `state`,
ADD COLUMN `two_factor_secret` VARCHAR(255) NULL DEFAULT NULL COMMENT 'TOTP密钥' AFTER `two_factor_enabled`,
ADD COLUMN `two_factor_setup_at` DATETIME(3) NULL DEFAULT NULL COMMENT '2FA设置时间' AFTER `two_factor_secret`;

-- 为2FA字段添加索引
CREATE INDEX `idx_admin_users_two_factor_enabled` ON `admin_users` (`two_factor_enabled`);
