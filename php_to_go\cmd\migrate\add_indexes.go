package main

import (
	"log"
	"os"

	"github.com/joho/godotenv"

	"go-fiber-api/database"
)

// indexExists 检查索引是否已存在
func indexExists(db *gorm.DB, tableName, indexName string) bool {
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = ? AND index_name = ?", tableName, indexName).Scan(&count).Error
	if err != nil {
		log.Printf("检查索引存在性失败: %v", err)
		return false
	}
	return count > 0
}

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告: 找不到.env文件")
	}

	// 初始化数据库连接
	if err := database.ConnDatabase(); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	db := database.GetDB()
	
	log.Println("开始创建性能优化索引...")

	// 定义索引创建语句 - MySQL不支持IF NOT EXISTS，所以我们需要先检查索引是否存在
	indexes := []struct {
		name      string
		indexName string
		tableName string
		query     string
	}{
		// admin_menu 表索引
		{"admin_menu parent_id", "idx_admin_menu_parent_id", "admin_menu", "CREATE INDEX idx_admin_menu_parent_id ON admin_menu(parent_id)"},
		{"admin_menu parent_order", "idx_admin_menu_parent_order", "admin_menu", "CREATE INDEX idx_admin_menu_parent_order ON admin_menu(parent_id, `order`)"},
		{"admin_menu title", "idx_admin_menu_title", "admin_menu", "CREATE INDEX idx_admin_menu_title ON admin_menu(title)"},

		// projects 表索引
		{"projects user_id", "idx_projects_user_id", "projects", "CREATE INDEX idx_projects_user_id ON projects(user_id)"},
		{"projects state", "idx_projects_state", "projects", "CREATE INDEX idx_projects_state ON projects(state)"},
		{"projects title", "idx_projects_title", "projects", "CREATE INDEX idx_projects_title ON projects(title)"},
		{"projects unique_key", "idx_projects_unique_key", "projects", "CREATE INDEX idx_projects_unique_key ON projects(unique_key)"},
		{"projects user_state", "idx_projects_user_state", "projects", "CREATE INDEX idx_projects_user_state ON projects(user_id, state)"},
		{"projects created_at", "idx_projects_created_at", "projects", "CREATE INDEX idx_projects_created_at ON projects(created_at)"},
		{"projects deleted_at", "idx_projects_deleted_at", "projects", "CREATE INDEX idx_projects_deleted_at ON projects(deleted_at)"},

		// projects_content 表索引
		{"projects_content project_id", "idx_projects_content_project_id", "projects_content", "CREATE INDEX idx_projects_content_project_id ON projects_content(project_id)"},
		{"projects_content user_id", "idx_projects_content_user_id", "projects_content", "CREATE INDEX idx_projects_content_user_id ON projects_content(user_id)"},
		{"projects_content project_user", "idx_projects_content_project_user", "projects_content", "CREATE INDEX idx_projects_content_project_user ON projects_content(project_id, user_id)"},
		{"projects_content deleted_at", "idx_projects_content_deleted_at", "projects_content", "CREATE INDEX idx_projects_content_deleted_at ON projects_content(deleted_at)"},

		// admin_role_menu 表索引
		{"admin_role_menu role_id", "idx_admin_role_menu_role_id", "admin_role_menu", "CREATE INDEX idx_admin_role_menu_role_id ON admin_role_menu(role_id)"},
		{"admin_role_menu menu_id", "idx_admin_role_menu_menu_id", "admin_role_menu", "CREATE INDEX idx_admin_role_menu_menu_id ON admin_role_menu(menu_id)"},

		// admin_role_users 表索引
		{"admin_role_users user_id", "idx_admin_role_users_user_id", "admin_role_users", "CREATE INDEX idx_admin_role_users_user_id ON admin_role_users(admin_user_id)"},
		{"admin_role_users role_id", "idx_admin_role_users_role_id", "admin_role_users", "CREATE INDEX idx_admin_role_users_role_id ON admin_role_users(role_id)"},

		// admin_roles 表索引
		{"admin_roles name", "idx_admin_roles_name", "admin_roles", "CREATE INDEX idx_admin_roles_name ON admin_roles(name)"},
		{"admin_roles all_menu_access", "idx_admin_roles_all_menu_access", "admin_roles", "CREATE INDEX idx_admin_roles_all_menu_access ON admin_roles(all_menu_access)"},

		// admin_users 表索引
		{"admin_users username", "idx_admin_users_username", "admin_users", "CREATE INDEX idx_admin_users_username ON admin_users(username)"},
		{"admin_users email", "idx_admin_users_email", "admin_users", "CREATE INDEX idx_admin_users_email ON admin_users(email)"},
		{"admin_users status", "idx_admin_users_status", "admin_users", "CREATE INDEX idx_admin_users_status ON admin_users(status)"},
	}

	// 执行索引创建
	successCount := 0
	failCount := 0
	skipCount := 0

	for _, idx := range indexes {
		log.Printf("检查索引: %s", idx.name)

		// 检查索引是否已存在
		if indexExists(db, idx.tableName, idx.indexName) {
			log.Printf("⏭️  索引已存在，跳过 [%s]", idx.name)
			skipCount++
			continue
		}

		log.Printf("创建索引: %s", idx.name)
		if err := db.Exec(idx.query).Error; err != nil {
			log.Printf("❌ 创建索引失败 [%s]: %v", idx.name, err)
			failCount++
		} else {
			log.Printf("✅ 创建索引成功 [%s]", idx.name)
			successCount++
		}
	}

	// 检查权限表是否存在并创建相应索引
	var tableExists int64
	db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'admin_permissions'").Scan(&tableExists)

	if tableExists > 0 {
		permissionIndexes := []struct {
			name      string
			indexName string
			tableName string
			query     string
		}{
			{"admin_permissions parent_id", "idx_admin_permissions_parent_id", "admin_permissions", "CREATE INDEX idx_admin_permissions_parent_id ON admin_permissions(parent_id)"},
			{"admin_permissions parent_order", "idx_admin_permissions_parent_order", "admin_permissions", "CREATE INDEX idx_admin_permissions_parent_order ON admin_permissions(parent_id, `order`)"},
			{"admin_permissions slug", "idx_admin_permissions_slug", "admin_permissions", "CREATE INDEX idx_admin_permissions_slug ON admin_permissions(slug)"},
		}

		for _, idx := range permissionIndexes {
			log.Printf("检查权限表索引: %s", idx.name)

			// 检查索引是否已存在
			if indexExists(db, idx.tableName, idx.indexName) {
				log.Printf("⏭️  权限表索引已存在，跳过 [%s]", idx.name)
				skipCount++
				continue
			}

			log.Printf("创建权限表索引: %s", idx.name)
			if err := db.Exec(idx.query).Error; err != nil {
				log.Printf("❌ 创建权限表索引失败 [%s]: %v", idx.name, err)
				failCount++
			} else {
				log.Printf("✅ 创建权限表索引成功 [%s]", idx.name)
				successCount++
			}
		}
	}

	// 输出结果统计
	log.Println("\n=== 索引创建完成 ===")
	log.Printf("成功: %d 个", successCount)
	log.Printf("跳过: %d 个", skipCount)
	log.Printf("失败: %d 个", failCount)
	log.Printf("总计: %d 个", successCount+skipCount+failCount)

	if failCount > 0 {
		log.Println("\n⚠️  部分索引创建失败，请检查上述错误信息")
		os.Exit(1)
	} else {
		log.Println("\n🎉 所有索引创建成功！")
	}
}
