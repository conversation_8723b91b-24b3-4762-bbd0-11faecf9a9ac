package dto

import "time"

// AuthSignupDTO 管理员注册请求数据结构
type AuthSignupDTO struct {
	Username       string `json:"username" validate:"required,min=3,max=120"`
	Password       string `json:"password" validate:"required,min=6,max=80"`
	Name           string `json:"name" validate:"required"`
	Email          string `json:"email" validate:"omitempty,email"`
	Referee        string `json:"referee"` // Made optional for validation, logic handled in handler
	<PERSON><PERSON>        string `json:"captcha" validate:"required"`
	CaptchaKey     string `json:"captcha_key" validate:"required"`

	InvitationCode string `json:"invitation_code"` // 邀请码字段，根据设置决定是否必填
}

// AuthLoginDTO 管理员登录请求数据结构
type AuthLoginDTO struct {
	Username      string `json:"username" validate:"required"`
	Password      string `json:"password" validate:"required"`
	Captcha       string `json:"captcha" validate:"required"`
	CaptchaKey    string `json:"captcha_key" validate:"required"`
	TwoFactorCode string `json:"two_factor_code"` // 双因素认证代码 (6位数字)
	IsAdminLogin  bool   `json:"is_admin_login"`  // 标记是否为管理员登录
	Remember      int    `json:"remember"`        // 记住我选项 (1=记住, 0=不记住)
}

// AuthForgotPasswordDTO 忘记密码请求数据结构
type AuthForgotPasswordDTO struct {
	Username       string `json:"username" validate:"required"`
	Email          string `json:"email" validate:"required,email"`
	Captcha        string `json:"captcha" validate:"required"`
	CaptchaKey     string `json:"captcha_key" validate:"required"`

}

// AuthResetPasswordDTO 重置密码请求数据结构
type AuthResetPasswordDTO struct {
	Token       string `json:"token" validate:"required"`
	NewPassword string `json:"new_password" validate:"required,min=6,max=80"`
}

// AuthResponse 认证成功的响应数据结构
type AuthResponse struct {
	Token    string      `json:"token"`
	UserInfo interface{} `json:"user_info,omitempty"` // 用户信息，可选
}

// CaptchaResponse 验证码生成接口的响应数据结构
type CaptchaResponse struct {
	CaptchaKey string `json:"captcha_key"` // 验证码唯一标识
	ImageData  string `json:"image_data"`  // Base64编码的图片数据 (e.g., data:image/png;base64,...)
}



// TwoFactorSetupResponse 2FA 设置响应数据结构
type TwoFactorSetupResponse struct {
	Secret    string `json:"secret"`      // TOTP 密钥
	QRCodeURL string `json:"qr_code_url"` // 二维码 URL
}

// TwoFactorVerifyDTO 2FA 验证请求数据结构
type TwoFactorVerifyDTO struct {
	Code string `json:"code" validate:"required,len=6"` // 6位数字验证码
}

// TwoFactorDisableDTO 2FA 禁用请求数据结构
type TwoFactorDisableDTO struct {
	Code string `json:"code" validate:"required,len=6"` // 6位数字验证码
}

// TwoFactorStatusResponse 2FA 状态响应数据结构
type TwoFactorStatusResponse struct {
	Enabled    bool       `json:"enabled"`     // 是否启用 2FA
	SetupAt    *time.Time `json:"setup_at"`    // 设置时间
	IsRequired bool       `json:"is_required"` // 是否必须设置
}
