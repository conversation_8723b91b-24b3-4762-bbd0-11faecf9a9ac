<template>
  <div class="two-factor-setup-container">
    <div class="setup-content">
      <div class="setup-header">
        <h2>设置双因素认证</h2>
        <p>为了保护您的账户安全，请设置双因素认证</p>
      </div>

      <div class="setup-steps">
        <!-- 步骤 1: 生成密钥 -->
        <el-card v-if="currentStep === 1" class="step-card">
          <div slot="header" class="card-header">
            <span>步骤 1: 生成认证密钥</span>
          </div>
          <div class="step-content">
            <p>点击下方按钮生成您的双因素认证密钥</p>
            <el-button 
              type="primary" 
              :loading="loading" 
              @click="generateSecret"
              size="large"
            >
              生成密钥
            </el-button>
          </div>
        </el-card>

        <!-- 步骤 2: 扫描二维码 -->
        <el-card v-if="currentStep === 2" class="step-card">
          <div slot="header" class="card-header">
            <span>步骤 2: 扫描二维码</span>
          </div>
          <div class="step-content">
            <p>使用 Google Authenticator 或其他 TOTP 应用扫描下方二维码：</p>
            
            <div class="qr-code-container">
              <div v-if="qrCodeUrl" class="qr-code">
                <img :src="qrCodeUrl" alt="Google Authenticator 二维码" @error="onQrCodeError" />
                <p class="qr-hint">请使用 Google Authenticator 或其他 TOTP 应用扫描此二维码</p>
              </div>
              <div v-else-if="loading" class="qr-loading">
                <i class="el-icon-loading"></i>
                <p>生成二维码中...</p>
              </div>
              <div v-else class="qr-error">
                <i class="el-icon-warning"></i>
                <p>二维码生成失败，请重试或使用手动输入密钥</p>
                <el-button type="primary" size="small" @click="generateSecret">重新生成</el-button>
              </div>
            </div>

            <div class="secret-info">
              <p><strong>手动输入密钥：</strong></p>
              <el-input 
                v-model="secretKey" 
                readonly 
                class="secret-input"
              >
                <el-button 
                  slot="append" 
                  @click="copySecret"
                  icon="el-icon-copy-document"
                >
                  复制
                </el-button>
              </el-input>
            </div>

            <div class="step-actions">
              <el-button @click="currentStep = 1">上一步</el-button>
              <el-button type="primary" @click="currentStep = 3">下一步</el-button>
            </div>
          </div>
        </el-card>

        <!-- 步骤 3: 验证设置 -->
        <el-card v-if="currentStep === 3" class="step-card">
          <div slot="header" class="card-header">
            <span>步骤 3: 验证设置</span>
          </div>
          <div class="step-content">
            <p>请输入您的认证应用显示的 6 位数字验证码：</p>
            
            <el-form 
              ref="verifyForm" 
              :model="verifyForm" 
              :rules="verifyRules"
              label-width="0"
            >
              <el-form-item prop="code">
                <el-input
                  v-model="verifyForm.code"
                  placeholder="请输入 6 位验证码"
                  maxlength="6"
                  class="verify-input"
                  @keyup.enter.native="verifySetup"
                >
                  <template slot="prepend">验证码</template>
                </el-input>
              </el-form-item>
            </el-form>

            <div class="step-actions">
              <el-button @click="currentStep = 2">上一步</el-button>
              <el-button 
                type="primary" 
                :loading="loading" 
                @click="verifySetup"
              >
                完成设置
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 设置完成 -->
        <el-card v-if="currentStep === 4" class="step-card success-card">
          <div class="step-content">
            <div class="success-icon">
              <i class="el-icon-success"></i>
            </div>
            <h3>双因素认证设置成功！</h3>
            <p>您的账户现在受到双因素认证保护</p>
            
            <div class="final-actions">
              <el-button type="primary" @click="goToDashboard">
                进入控制面板
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 进度指示器 -->
      <div class="progress-indicator">
        <el-steps :active="currentStep - 1" finish-status="success">
          <el-step title="生成密钥"></el-step>
          <el-step title="扫描二维码"></el-step>
          <el-step title="验证设置"></el-step>
          <el-step title="完成"></el-step>
        </el-steps>
      </div>
    </div>
  </div>
</template>

<script>
import { generateTwoFactorSecret, verifyTwoFactorSetup } from '@/api/auth'

export default {
  name: 'TwoFactorSetup',
  data() {
    return {
      currentStep: 1,
      loading: false,
      secretKey: '',
      qrCodeUrl: '',
      verifyForm: {
        code: ''
      },
      verifyRules: {
        code: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { len: 6, message: '验证码必须是 6 位数字', trigger: 'blur' },
          { pattern: /^\d{6}$/, message: '验证码只能包含数字', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 检查用户是否已登录或有临时 token
    const tempToken = localStorage.getItem('temp_token')
    if (!this.$store.getters.isLoggedIn && !tempToken) {
      this.$router.push('/login')
      return
    }

    // 如果有临时 token，说明是首次登录需要设置 2FA
    if (tempToken) {
      this.$message.info('请完成双因素认证设置以确保账户安全')
    }
  },
  methods: {
    // 生成密钥
    generateSecret() {
      this.loading = true
      generateTwoFactorSecret()
        .then((response) => {
          this.secretKey = response.secret
          this.qrCodeUrl = response.qr_code_url
          this.currentStep = 2
          this.$message.success('密钥生成成功')
        })
        .catch((error) => {
          console.error('生成密钥失败:', error)
          this.$message.error('生成密钥失败，请重试')
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 复制密钥
    copySecret() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.secretKey).then(() => {
          this.$message.success('密钥已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopySecret()
        })
      } else {
        this.fallbackCopySecret()
      }
    },

    // 备用复制方法
    fallbackCopySecret() {
      const textArea = document.createElement('textarea')
      textArea.value = this.secretKey
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('密钥已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    },

    // 验证设置
    verifySetup() {
      this.$refs.verifyForm.validate((valid) => {
        if (valid) {
          this.loading = true
          verifyTwoFactorSetup({
            code: this.verifyForm.code
          })
            .then((response) => {
              this.currentStep = 4
              this.$message.success('双因素认证设置成功')

              // 如果是使用临时 token 设置的，需要处理正式登录
              const tempToken = localStorage.getItem('temp_token')
              if (tempToken) {
                // 清除临时 token
                localStorage.removeItem('temp_token')

                // 如果响应中包含正式的 token，保存它并跳转到首页
                if (response && response.token) {
                  this.$store.dispatch('login', response).then(() => {
                    this.$message.success('2FA设置完成，登录成功！')
                    // 跳转到首页或指定页面
                    const redirectUrl = this.$route.query.redirect || '/'
                    this.$router.push(redirectUrl)
                  })
                } else {
                  // 如果没有返回token，跳转到登录页面
                  this.$message.warning('2FA设置完成，请重新登录')
                  this.$router.push('/login')
                }
              }
            })
            .catch((error) => {
              console.error('验证失败:', error)
              let errorMsg = '验证失败，请重试'
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg
              }
              this.$message.error(errorMsg)
              this.verifyForm.code = ''
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },

    // 跳转到控制面板
    goToDashboard() {
      const redirectUrl = this.$route.query.redirect || '/'
      this.$router.push(redirectUrl)
    },

    // 二维码加载错误处理
    onQrCodeError() {
      console.error('二维码图片加载失败')
      this.$message.error('二维码显示失败，请使用手动输入密钥')
    }
  }
}
</script>

<style scoped>
.two-factor-setup-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.setup-content {
  width: 100%;
  max-width: 600px;
}

.setup-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.setup-header h2 {
  font-size: 2rem;
  margin-bottom: 10px;
}

.setup-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.step-card {
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
  font-weight: 600;
  font-size: 1.1rem;
}

.step-content {
  text-align: center;
  padding: 20px;
}

.qr-code-container {
  margin: 20px 0;
  text-align: center;
}

.qr-code {
  display: inline-block;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.qr-code img {
  width: 256px;
  height: 256px;
  border: none;
  border-radius: 8px;
  display: block;
}

.qr-hint {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.qr-loading {
  padding: 40px;
  color: #666;
  text-align: center;
}

.qr-loading i {
  font-size: 24px;
  margin-bottom: 10px;
}

.qr-error {
  padding: 40px;
  color: #f56c6c;
  text-align: center;
}

.qr-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.secret-info {
  margin: 20px 0;
  text-align: left;
}

.secret-input {
  margin-top: 10px;
}

.verify-input {
  max-width: 300px;
  margin: 0 auto;
}

.step-actions {
  margin-top: 30px;
}

.step-actions .el-button {
  margin: 0 10px;
}

.success-card {
  text-align: center;
}

.success-icon {
  font-size: 4rem;
  color: #67c23a;
  margin-bottom: 20px;
}

.success-icon i {
  font-size: 4rem;
}

.final-actions {
  margin-top: 30px;
}

.progress-indicator {
  margin-top: 30px;
  background: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .setup-content {
    max-width: 100%;
  }
  
  .setup-header h2 {
    font-size: 1.5rem;
  }
  
  .qr-code img {
    max-width: 150px;
  }
}
</style>
