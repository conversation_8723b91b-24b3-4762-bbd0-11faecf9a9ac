{"name": "dome", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.9.0", "core-js": "^3.8.3", "element-ui": "^2.15.14", "nprogress": "^0.2.0", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}