/**
 * 处理图片URL，确保正确显示
 * @param {string} url - 原始URL
 * @returns {string} - 处理后的完整URL
 */
export function getImageUrl(url) {
  if (!url) return '';
  
  // 如果已经是完整URL，直接返回
  if (url.startsWith('http') || url.startsWith('https')) {
    return url;
  }
  
  // 添加后端基础URL
  const baseUrl = 'http://38.148.254.188:3000';
  
  // 确保URL格式正确
  if (url.startsWith('/')) {
    return baseUrl + url;
  } else {
    return baseUrl + '/' + url;
  }
}

/**
 * 格式化日期时间
 * @param {string|Date} date - 日期对象或日期字符串
 * @param {string} [format='YYYY-MM-DD HH:mm:ss'] - 格式化模式
 * @returns {string} - 格式化后的日期字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 截断文本
 * @param {string} text - 原始文本
 * @param {number} length - 最大长度
 * @param {string} [suffix='...'] - 截断后的后缀
 * @returns {string} - 截断后的文本
 */
export function truncateText(text, length, suffix = '...') {
  if (!text) return '';
  return text.length > length ? text.substring(0, length) + suffix : text;
} 