services:
  # MySQL服务 - 简化版本
  mysql:
    image: mysql:8.0
    container_name: go-fiber-mysql-simple
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: go_fiber
    ports:
      - "3308:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - go-fiber-network
    command: --default-authentication-plugin=mysql_native_password

networks:
  go-fiber-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
