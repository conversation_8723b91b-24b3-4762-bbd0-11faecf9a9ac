<template>
  <div class="forgot-container">
    <!-- 波浪背景效果 -->
    <div class="wave-container">
      <div class="wave wave1"></div>
      <div class="wave wave2"></div>
      <div class="wave wave3"></div>
    </div>
    
    <div class="forgot-content">
      <!-- 左侧品牌展示区 -->
      <div class="brand-section">
        <div class="brand-content">
          <h1 class="brand-title">XSS</h1>
          <h2 class="brand-subtitle">安全测试平台</h2>
          <p class="brand-description">
            找回您的账号密码，重新获得平台访问权限。
          </p>
          
          <div class="features">
            <div class="feature-item">
              <i class="el-icon-refresh"></i>
              <span>快速找回密码</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-message"></i>
              <span>邮件验证重置</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-key"></i>
              <span>安全身份验证</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-header">
          <h2>找回密码</h2>
          <p>请填写您的账号信息，我们将发送重置密码邮件</p>
        </div>
        
        <div class="form-container">
          <el-form :model="forgotForm" :rules="rules" ref="forgotForm" label-width="0" @submit.native.prevent>
            <!-- 用户名 -->
            <el-form-item prop="username">
              <el-input 
                v-model="forgotForm.username" 
                placeholder="用户名" 
                prefix-icon="el-icon-user">
              </el-input>
            </el-form-item>
            
            <!-- 邮箱 -->
            <el-form-item prop="email">
              <el-input 
                v-model="forgotForm.email" 
                placeholder="邮箱" 
                prefix-icon="el-icon-message">
              </el-input>
            </el-form-item>
            
            <!-- 验证码 -->
            <el-form-item prop="captcha">
              <div class="captcha-container">
                <el-input v-model="forgotForm.captcha" placeholder="验证码" prefix-icon="el-icon-key"></el-input>
                <div class="captcha-image" @click="refreshCaptcha">
                  <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                  <div v-else class="captcha-loading">
                    <i class="el-icon-loading"></i>
                  </div>
                </div>
              </div>
            </el-form-item>
            
            <!-- 提交按钮 -->
            <el-form-item>
              <el-button type="primary" :loading="loading" class="submit-button" @click="handleSubmit">
                提交 <i class="el-icon-right"></i>
              </el-button>
            </el-form-item>
            
            <!-- 额外链接 -->
            <div class="form-links">
              <router-link to="/login" class="link">
                <i class="el-icon-back"></i> 返回登录
              </router-link>
              <router-link to="/signup" class="link">
                <i class="el-icon-plus"></i> 注册新账号
              </router-link>
            </div>
          </el-form>
        </div>
        
        <!-- 版权信息 -->
        <div class="copyright">
          © {{ new Date().getFullYear() }} XSS测试平台
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCaptcha, forgotPassword } from '@/api/auth'

export default {
  name: 'ForgotPassword',
  data() {
    return {
      loading: false,
      captchaImage: '',
      captchaKey: '',
      forgotForm: {
        username: '',
        email: '',
        captcha: '',
        captcha_key: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 获取验证码
    this.refreshCaptcha()
  },
  mounted() {
    // 窗口大小改变时重新调整布局
    window.addEventListener('resize', this.handleResize)
    this.handleResize()
  },
  beforeDestroy() {
    // 清除事件监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 处理窗口大小改变
    handleResize() {
      // 可以根据窗口大小调整UI
    },
    
    // 刷新验证码
    refreshCaptcha() {
      getCaptcha().then(res => {
        this.captchaImage = res.image_data
        this.captchaKey = res.captcha_key
        this.forgotForm.captcha_key = res.captcha_key
      }).catch(() => {
        this.$message.error('获取验证码失败，请重试')
      })
    },
    
    // 处理找回密码逻辑
    handleSubmit() {
      this.$refs.forgotForm.validate(valid => {
        if (valid) {
          this.loading = true
          
          forgotPassword(this.forgotForm)
            .then(() => {
              this.$message({
                type: 'success',
                message: '密码重置邮件已发送，请检查您的邮箱'
              })
              setTimeout(() => {
                this.$router.push('/login')
              }, 1500)
            })
            .catch(error => {
              // 提取更详细的错误信息
              let errorMsg = '找回密码失败，请重试'
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg
              } else if (error.message) {
                errorMsg = error.message
              }
              this.$message.error(errorMsg)
              
              // 刷新验证码
              this.refreshCaptcha()
              this.forgotForm.captcha = ''
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>

<style scoped>
.forgot-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 波浪背景效果 */
.wave-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background-repeat: repeat-x;
  background-position: 0 bottom;
  transform-origin: center bottom;
}

.wave1 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23a4dbff" fill-opacity="0.2" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,261.3C672,256,768,224,864,213.3C960,203,1056,213,1152,224C1248,235,1344,245,1392,250.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  animation: wave 15s linear infinite;
  z-index: 1;
  opacity: 0.6;
  animation-delay: 0s;
  bottom: 0;
}

.wave2 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%238ed1fc" fill-opacity="0.3" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,106.7C672,117,768,171,864,181.3C960,192,1056,160,1152,149.3C1248,139,1344,149,1392,154.7L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  animation: wave 20s linear infinite;
  z-index: 2;
  opacity: 0.4;
  animation-delay: -5s;
  bottom: 0;
}

.wave3 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%236cb2eb" fill-opacity="0.2" d="M0,256L48,229.3C96,203,192,149,288,154.7C384,160,480,224,576,234.7C672,245,768,203,864,181.3C960,160,1056,160,1152,154.7C1248,149,1344,139,1392,133.3L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  animation: wave 30s linear infinite;
  z-index: 3;
  opacity: 0.3;
  animation-delay: -2s;
  bottom: 0;
}

@keyframes wave {
  0% {
    transform: translateX(0) translateZ(0) scaleY(1);
  }
  50% {
    transform: translateX(-25%) translateZ(0) scaleY(0.8);
  }
  100% {
    transform: translateX(-50%) translateZ(0) scaleY(1);
  }
}

.forgot-content {
  display: flex;
  width: 80%;
  max-width: 1200px;
  background-color: #ffffff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
}

/* 左侧品牌区 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #3f87a6, #4b6cb7);
  color: #fff;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.brand-content {
  max-width: 400px;
  text-align: center;
}

.brand-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  letter-spacing: 2px;
}

.brand-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  margin-bottom: 20px;
  letter-spacing: 1px;
}

.brand-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 30px;
  opacity: 0.9;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s;
}

.feature-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.feature-item i {
  font-size: 1.5rem;
}

/* 右侧表单区 */
.form-section {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.form-header p {
  font-size: 1rem;
  color: #666;
}

.form-container {
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

/* 表单元素样式 */
.form-container /deep/ .el-input__inner {
  border-radius: 8px;
  padding: 12px 15px;
  height: 48px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s;
}

.form-container /deep/ .el-input__inner:focus {
  border-color: #4b6cb7;
  box-shadow: 0 0 0 2px rgba(75, 108, 183, 0.1);
}

.form-container /deep/ .el-input__prefix {
  left: 15px;
}

.form-container /deep/ .el-input--prefix .el-input__inner {
  padding-left: 45px;
}

.captcha-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.captcha-image {
  width: 120px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border: 1px solid #e0e0e0;
}

.captcha-image img {
  max-width: 100%;
  max-height: 100%;
}

.captcha-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* 按钮样式 */
.submit-button {
  width: 100%;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 1px;
  height: auto;
  transition: all 0.3s;
  background: linear-gradient(to right, #4b6cb7, #3f87a6);
  border: none;
}

.submit-button:hover {
  background: linear-gradient(to right, #3f57a3, #2d7491);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(75, 108, 183, 0.3);
}

/* 链接样式 */
.form-links {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.link {
  color: #4b6cb7;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.link:hover {
  color: #3f57a3;
  text-decoration: underline;
}

.copyright {
  margin-top: 40px;
  text-align: center;
  color: #999;
  font-size: 0.85rem;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .forgot-content {
    flex-direction: column;
    width: 90%;
  }
  
  .brand-section {
    padding: 30px 20px;
  }
  
  .form-section {
    padding: 30px 20px;
  }
}

@media (max-width: 576px) {
  .forgot-content {
    width: 95%;
  }
  
  .brand-section {
    padding: 20px 15px;
  }
  
  .brand-title {
    font-size: 2.5rem;
  }
  
  .form-section {
    padding: 20px 15px;
  }
  
  .form-header h2 {
    font-size: 1.5rem;
  }
}
</style> 