import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'
import { getUserMenuTree } from '@/api/menu'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// NProgress配置
NProgress.configure({ showSpinner: false })

Vue.use(VueRouter)

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
}


// 路由懒加载
const Home = () => import('@/views/Home.vue')
const About = () => import('@/views/About.vue')
const Login = () => import('@/views/auth/Login.vue')
const Signup = () => import('@/views/auth/Signup.vue')
const ForgotPassword = () => import('@/views/auth/ForgotPassword.vue')
const TwoFactorSetup = () => import('@/views/auth/TwoFactorSetup.vue')


// 错误页面
const Forbidden = () => import('@/views/errors/403.vue')

// XSS平台功能页面
const Dashboard = () => import('@/views/dashboard/Index.vue')
const ProjectList = () => import('@/views/project/List.vue')
const ProjectCreate = () => import('@/views/project/Create.vue')
const ProjectDetail = () => import('@/views/project/Detail.vue')
const ProjectCode = () => import('@/views/project/Code.vue')
const ProjectContentList = () => import('@/views/projectContent/List.vue')
const ProjectContentEdit = () => import('@/views/projectContent/Edit.vue')
const ModuleList = () => import('@/views/module/List.vue')
const ModuleCreate = () => import('@/views/module/Create.vue')
const ModuleDetail = () => import('@/views/module/Detail.vue')
const DomainList = () => import('@/views/domain/List.vue')
const DomainCreate = () => import('@/views/domain/Create.vue')
const Settings = () => import('@/views/settings/Index.vue')
const UserProfile = () => import('@/views/user/Profile.vue')

// 任务管理页面
const JobList = () => import('@/views/job/List.vue')
const FailedJobList = () => import('@/views/job/FailedList.vue')

// 公告管理页面
const ArticleList = () => import('@/views/article/List.vue')
const ArticleEdit = () => import('@/views/article/Edit.vue')
const ArticleDetail = () => import('@/views/article/Detail.vue')

// 邀请码管理页面
const InvitationList = () => import('@/views/invitation/Index.vue')
const InvitationCreate = () => import('@/views/invitation/Create.vue')

  // 管理员用户管理页面
const AdminUserList = () => import('@/views/admin/user/index.vue')
const AdminUserDetail = () => import('@/views/admin/user/detail.vue')
const RoleList = () => import('@/views/admin/role/index.vue')
const PermissionList = () => import('@/views/admin/permission/index.vue')

// 友情链接管理页面
const FriendlyList = () => import('@/views/friendly/List.vue')
const FriendlyEdit = () => import('@/views/friendly/Edit.vue')

// 菜单管理页面
const MenuList = () => import('@/views/menu/List.vue')
const MenuCreate = () => import('@/views/menu/Create.vue')
const MenuEdit = () => import('@/views/menu/Edit.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '控制面板',
      requiresAuth: true
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '控制面板',
      requiresAuth: true
    }
  },
  {
    path: '/project',
    name: 'ProjectList',
    component: ProjectList,
    meta: {
      title: '项目列表',
      requiresAuth: true
    }
  },
  {
    path: '/project/create',
    name: 'ProjectCreate',
    component: ProjectCreate,
    meta: {
      title: '创建项目',
      requiresAuth: true
    }
  },
  {
    path: '/project/:id',
    name: 'ProjectDetail',
    component: ProjectDetail,
    meta: {
      title: '项目详情',
      requiresAuth: true
    }
  },
  {
    path: '/project/:id/code',
    name: 'ProjectCode',
    component: ProjectCode,
    meta: {
      title: '项目代码',
      requiresAuth: true
    }
  },
  {
    path: '/module',
    name: 'ModuleList',
    component: ModuleList,
    meta: {
      title: '模块列表',
      requiresAuth: true
    }
  },
  {
    path: '/module/create',
    name: 'ModuleCreate',
    component: ModuleCreate,
    meta: {
      title: '创建模块',
      requiresAuth: true
    }
  },
  {
    path: '/module/:id',
    name: 'ModuleDetail',
    component: ModuleDetail,
    meta: {
      title: '模块详情',
      requiresAuth: true
    }
  },
  {
    path: '/domain',
    name: 'DomainList',
    component: DomainList,
    meta: {
      title: '域名管理',
      requiresAuth: true
    }
  },
  {
    path: '/domain/create',
    name: 'DomainCreate',
    component: DomainCreate,
    meta: {
      title: '添加域名',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '系统设置',
      requiresAuth: true
    }
  },
  {
    path: '/user/profile',
    name: 'UserProfile',
    component: UserProfile,
    meta: {
      title: '个人资料',
      requiresAuth: true
    }
  },
  {
    path: '/about',
    name: 'About',
    component: About,
    meta: {
      title: '关于',
      requiresAuth: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录'
    }
  },
  {
    path: '/signup',
    name: 'Signup',
    component: Signup,
    meta: {
      title: '注册'
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: ForgotPassword,
    meta: {
      title: '找回密码'
    }
  },
  {
    path: '/article/list',
    name: 'ArticleList',
    component: ArticleList,
    meta: {
      title: '公告列表',
      requiresAuth: true
    }
  },
  {
    path: '/article/create',
    name: 'ArticleCreate',
    component: ArticleEdit,
    meta: {
      title: '创建公告',
      requiresAuth: true
    }
  },
  {
    path: '/article/edit/:id',
    name: 'ArticleEdit',
    component: ArticleEdit,
    meta: {
      title: '编辑公告',
      requiresAuth: true
    }
  },
  {
    path: '/article/detail/:id',
    name: 'ArticleDetail',
    component: ArticleDetail,
    meta: {
      title: '公告详情',
      requiresAuth: true
    }
  },
  {
    path: '/admin/users',
    name: 'AdminUserList',
    component: AdminUserList,
    meta: {
      title: '管理员用户列表',
      requiresAuth: true,
      isAdmin: true
    }
  },
  {
    path: '/admin/users/:id',
    name: 'AdminUserDetail',
    component: AdminUserDetail,
    meta: {
      title: '管理员用户详情',
      requiresAuth: true,
      isAdmin: true
    }
  },
  {
    path: '/admin/roles',
    name: 'RoleList',
    component: RoleList,
    meta: {
      title: '角色管理',
      requiresAuth: true,
      isAdmin: true
    }
  },
  {
    path: '/admin/permissions',
    name: 'PermissionList',
    component: PermissionList,
    meta: {
      title: '权限管理',
      requiresAuth: true,
      isAdmin: true
    }
  },
  {
    path: '/invitation',
    name: 'InvitationList',
    component: InvitationList,
    meta: {
      title: '邀请码列表',
      requiresAuth: true
    }
  },
  {
    path: '/invitation/create',
    name: 'InvitationCreate',
    component: InvitationCreate,
    meta: {
      title: '创建邀请码',
      requiresAuth: true
    }
  },
  {
    path: '/invitation/edit',
    name: 'InvitationEdit',
    component: InvitationCreate,
    meta: {
      title: '编辑邀请码',
      requiresAuth: true
    }
  },
  {
    path: '/project-content',
    name: 'ProjectContentList',
    component: ProjectContentList,
    meta: {
      title: '项目内容列表',
      requiresAuth: true
    }
  },
  {
    path: '/project-content/create',
    name: 'ProjectContentCreate',
    component: ProjectContentEdit,
    meta: {
      title: '创建项目内容',
      requiresAuth: true
    }
  },
  {
    path: '/project-content/edit/:id',
    name: 'ProjectContentEdit',
    component: ProjectContentEdit,
    meta: {
      title: '编辑项目内容',
      requiresAuth: true
    }
  },
  {
    path: '/project-content/:id',
    name: 'ProjectContentDetail',
    component: ProjectContentEdit,
    meta: {
      title: '项目内容详情',
      requiresAuth: true
    }
  },
  {
    path: '/friendly',
    name: 'FriendlyList',
    component: FriendlyList,
    meta: {
      title: '友情链接管理',
      requiresAuth: true
    }
  },
  {
    path: '/friendly/create',
    name: 'FriendlyCreate',
    component: FriendlyEdit,
    meta: {
      title: '创建友情链接',
      requiresAuth: true
    }
  },
  {
    path: '/friendly/edit/:id',
    name: 'FriendlyEdit',
    component: FriendlyEdit,
    meta: {
      title: '编辑友情链接',
      requiresAuth: true
    }
  },
  {
    path: '/job',
    name: 'JobList',
    component: JobList,
    meta: {
      title: '任务管理',
      requiresAuth: true,
      isAdmin: true
    }
  },
  {
    path: '/job/failed',
    name: 'FailedJobList',
    component: FailedJobList,
    meta: {
      title: '失败任务管理',
      requiresAuth: true,
      isAdmin: true
    }
  },
  {
    path: '/menu',
    name: 'MenuList',
    component: MenuList,
    meta: {
      title: '菜单管理',
      requiresAuth: true
    }
  },
  {
    path: '/menu/create',
    name: 'MenuCreate',
    component: MenuCreate,
    meta: {
      title: '创建菜单',
      requiresAuth: true
    }
  },
  {
    path: '/menu/edit',
    name: 'MenuEdit',
    component: MenuEdit,
    meta: {
      title: '编辑菜单',
      requiresAuth: true
    }
  },
  {
    path: '/2fa/setup',
    name: 'TwoFactorSetup',
    component: TwoFactorSetup,
    meta: {
      title: '设置双因素认证',
      requiresAuth: true
    }
  },

  {
    path: '/403',
    name: 'Forbidden',
    component: Forbidden,
    meta: {
      title: '权限不足'
    }
  },
  {
    path: '*',
    redirect: '/login'
  }
]

const router = new VueRouter({
  mode: 'hash', // 可选 'history'
  base: '/',
  routes
})

// 权限菜单映射表，用于缓存用户可访问的菜单路径
let permissionRoutes = []

// 获取用户菜单并保存可访问路径
async function loadUserMenu() {
  try {
    const response = await getUserMenuTree()
    // 递归提取所有可访问的路径
    permissionRoutes = extractRoutes(response)
    return permissionRoutes
  } catch (error) {
    console.error('获取用户菜单失败:', error)
    return []
  }
}

// 递归提取路由路径
function extractRoutes(menuItems) {
  let routes = []
  
  if (!Array.isArray(menuItems)) {
    return routes
  }
  
  menuItems.forEach(item => {
    if (item.uri) {
      routes.push(item.uri)
    }
    if (item.children && item.children.length > 0) {
      routes = routes.concat(extractRoutes(item.children))
    }
  })
  return routes
}

// 路由前置守卫
router.beforeEach(async (to, from, next) => {
  // 开始进度条
  NProgress.start()
  
  // 设置页面标题
  document.title = to.meta.title || 'XSS测试平台'
  
  // 确保token状态已经检查过
  if (!store.getters.hasTokenValidated) {
    try {
      await store.dispatch('checkToken')
    } catch (error) {
      console.error('验证令牌时出错:', error)
    }
  }
  
  // 记住我功能：检查是否有记住我标记
  const isRemembered = localStorage.getItem('remember_me') === '1'
  
  // 路由白名单，无需权限即可访问
  const whiteList = ['/login', '/signup', '/forgot-password', '/reset-password', '/2fa/setup', '/403', '/404', '/500']
  
  // 白名单直接放行
  if (whiteList.includes(to.path)) {
    next()
    NProgress.done()
    return
  }
  
  // 检查是否已登录
  const token = localStorage.getItem('token')
  if (!token) {
    // 未登录则重定向到登录页面
    next({
      path: '/login',
      query: { redirect: to.fullPath } // 将原目标路由作为query参数传入登录页
    })
    NProgress.done()
    return
  }
  
  // 已登录，检查是否有用户信息
  const userStr = localStorage.getItem('user')
  if (!userStr && to.path !== '/logout') {
    // 如果没有用户信息但有token，并且有"记住我"标记，尝试重新验证
    if (isRemembered) {
      try {
        // 尝试验证token有效性
        const isValid = await store.dispatch('checkToken')
        if (isValid) {
          next() // 如果token有效，直接放行
          NProgress.done()
          return
        }
      } catch (error) {
        console.error('记住我验证失败:', error)
      }
    }
    
    // 如果没有用户信息且没有记住我，或记住我验证失败，则清除token并重定向
    localStorage.removeItem('token')
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    NProgress.done()
    return
  }
  
  // 检查该路由是否需要登录权限
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 加载用户菜单和权限（如果尚未加载）
    if (permissionRoutes.length === 0) {
      console.log("加载用户菜单权限...");
      await loadUserMenu();
      console.log("获取到的可访问路径:", permissionRoutes);
    }
    
    // 检查用户是否为管理员
    let isAdmin = false;
    try {
      const user = JSON.parse(userStr);
      isAdmin = user && (user.role === 1 || user.is_admin === true);
      console.log("用户是否为管理员:", isAdmin);
    } catch (error) {
      console.error('解析用户信息失败:', error);
    }
    
    // 管理员拥有所有权限
    if (isAdmin) {
      console.log("用户是管理员，允许访问所有路由");
      next();
      return;
    }
    
    // 非管理员需要检查是否有权限访问当前路由
    console.log("检查路由权限，当前路径:", to.path);
    
    const hasPermission = permissionRoutes.some(route => {
      // 精确匹配或者是子路由匹配
      const match = to.path === route || to.path.startsWith(`${route}/`);
      if (match) {
        console.log("匹配到路由权限:", route);
      }
      return match;
    }) || to.path === '/' || to.path === '/dashboard'; // 首页和控制面板默认允许访问
    
    if (hasPermission) {
      console.log("用户有权限访问该路由");
      next();
    } else {
      console.log("用户无权限访问该路由，重定向到403页面");
      next('/403'); // 无权限页面
    }
  } else {
    // 如果访问的是登录页面，但已经登录，则重定向到首页
    if (to.path === '/login' && token) {
      next({ path: '/' })
    } else {
      next() // 不需要权限的页面直接访问
    }
  }
  
  NProgress.done()
})

// 路由后置钩子
router.afterEach(() => {
  // 结束进度条
  NProgress.done()
})

export default router 