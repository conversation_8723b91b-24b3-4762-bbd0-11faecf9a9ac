package handlers

import (
	"bytes"
	"crypto/rand"
	"encoding/base32"
	"encoding/base64"
	"fmt"
	"go-fiber-api/api/dto"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"go-fiber-api/utils"
	"image/png"
	"log"
	"strings"
	"time"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/qr"
	"github.com/gofiber/fiber/v2"
	"github.com/pquerna/otp/totp"
)

// GenerateTwoFactorSecret 生成 2FA 密钥和二维码
// @Summary 生成双因素认证密钥
// @Description 为当前用户生成 TOTP 密钥和二维码，用于设置 Google Authenticator
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} dto.StandardResponse{data=dto.TwoFactorSetupResponse} "成功响应"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Failure 500 {object} dto.StandardResponse "生成密钥失败"
// @Router /admin/auth/2fa/generate [post]
func GenerateTwoFactorSecret(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 生成随机密钥
	secret := make([]byte, 20)
	_, err := rand.Read(secret)
	if err != nil {
		log.Printf("生成随机密钥失败: %v", err)
		return utils.ServerError(c, "生成密钥失败", err)
	}

	// 将密钥编码为 Base32
	secretKey := base32.StdEncoding.EncodeToString(secret)

	// 生成 TOTP 密钥
	key, err := totp.Generate(totp.GenerateOpts{
		Issuer:      "XSS测试平台",
		AccountName: user.Username,
		Secret:      secret,
	})
	if err != nil {
		log.Printf("生成 TOTP 密钥失败: %v", err)
		return utils.ServerError(c, "生成 TOTP 密钥失败", err)
	}

	// 生成二维码图片
	qrCode, err := qr.Encode(key.URL(), qr.M, qr.Auto)
	if err != nil {
		log.Printf("生成二维码失败: %v", err)
		return utils.ServerError(c, "生成二维码失败", err)
	}

	// 缩放二维码到合适大小
	qrCode, err = barcode.Scale(qrCode, 256, 256)
	if err != nil {
		log.Printf("缩放二维码失败: %v", err)
		return utils.ServerError(c, "缩放二维码失败", err)
	}

	// 将二维码转换为PNG格式的base64字符串
	var buf bytes.Buffer
	if err := png.Encode(&buf, qrCode); err != nil {
		log.Printf("编码二维码失败: %v", err)
		return utils.ServerError(c, "编码二维码失败", err)
	}

	// 生成base64数据URL
	qrCodeDataURL := fmt.Sprintf("data:image/png;base64,%s", base64.StdEncoding.EncodeToString(buf.Bytes()))

	// 临时存储密钥到 Redis（10分钟过期）
	tempKey := fmt.Sprintf("2fa_setup:%d", userID)
	if err := utils.StoreTemporaryData(tempKey, secretKey, 10*time.Minute); err != nil {
		log.Printf("存储临时密钥失败: %v", err)
		return utils.ServerError(c, "存储临时密钥失败", err)
	}

	return utils.Success(c, "2FA 密钥生成成功", dto.TwoFactorSetupResponse{
		Secret:    secretKey,
		QRCodeURL: qrCodeDataURL,
	})
}

// VerifyTwoFactorSetup 验证并启用 2FA
// @Summary 验证并启用双因素认证
// @Description 验证用户输入的 TOTP 代码，如果正确则启用双因素认证
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param setup body dto.TwoFactorVerifyDTO true "验证数据"
// @Success 200 {object} dto.StandardResponse "启用成功"
// @Failure 400 {object} dto.StandardResponse "验证码错误"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/auth/2fa/verify [post]
func VerifyTwoFactorSetup(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 解析请求数据
	verifyDTO := new(dto.TwoFactorVerifyDTO)
	if err := c.BodyParser(verifyDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证输入
	if verifyDTO.Code == "" {
		return utils.BadRequest(c, "验证码不能为空", nil)
	}

	// 从 Redis 获取临时密钥
	tempKey := fmt.Sprintf("2fa_setup:%d", userID)
	secretKey, found := utils.RetrieveTemporaryData(tempKey)
	if !found {
		return utils.BadRequest(c, "密钥已过期，请重新生成", nil)
	}

	// 验证 TOTP 代码
	valid := totp.Validate(verifyDTO.Code, secretKey)
	if !valid {
		return utils.BadRequest(c, "验证码错误，请重试", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 启用 2FA
	now := time.Now()
	user.TwoFactorEnabled = true
	user.TwoFactorSecret = secretKey
	user.TwoFactorSetupAt = &now

	if err := database.DB.Save(&user).Error; err != nil {
		log.Printf("保存用户 2FA 设置失败: %v", err)
		return utils.ServerError(c, "启用双因素认证失败", err)
	}

	// 清除临时密钥
	utils.ClearTemporaryData(tempKey)

	log.Printf("User '%s' (ID=%d) enabled 2FA successfully", user.Username, user.ID)

	// 检查是否是首次设置（通过检查是否为临时 token）
	authHeader := c.Get("Authorization")
	isFirstTimeSetup := false

	if authHeader != "" {
		parts := strings.Split(authHeader, " ")
		if len(parts) == 2 && parts[0] == "Bearer" {
			tokenString := parts[1]
			// 尝试解析为临时 token
			_, err := utils.ParseTempToken(tokenString)
			isFirstTimeSetup = (err == nil)
		}
	}

	responseData := fiber.Map{
		"message": "双因素认证启用成功",
	}

	// 如果是首次设置，生成正式的 JWT token
	if isFirstTimeSetup {
		// 生成正式的 JWT token（使用标准的 GenerateToken 函数）
		token, err := utils.GenerateToken(user.ID, user.Username, user.Name)
		if err != nil {
			log.Printf("生成JWT失败: %v", err)
			return utils.ServerError(c, "生成认证令牌失败", err)
		}

		responseData["token"] = token
		responseData["user_info"] = fiber.Map{
			"id":                 user.ID,
			"username":           user.Username,
			"name":               user.Name,
			"email":              user.Email,
			"avatar":             user.Avatar,
			"is_admin":           user.ID == 1, // 简单判断是否为超级管理员
			"two_factor_enabled": true,
		}
	}

	return utils.Success(c, "双因素认证启用成功", responseData)
}

// DisableTwoFactor 禁用 2FA
// @Summary 禁用双因素认证
// @Description 禁用当前用户的双因素认证
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param disable body dto.TwoFactorDisableDTO true "禁用数据"
// @Success 200 {object} dto.StandardResponse "禁用成功"
// @Failure 400 {object} dto.StandardResponse "验证码错误"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/auth/2fa/disable [post]
func DisableTwoFactor(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 解析请求数据
	disableDTO := new(dto.TwoFactorDisableDTO)
	if err := c.BodyParser(disableDTO); err != nil {
		return utils.BadRequest(c, "无效的请求数据", err)
	}

	// 验证输入
	if disableDTO.Code == "" {
		return utils.BadRequest(c, "验证码不能为空", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 检查用户是否启用了 2FA
	if !user.TwoFactorEnabled {
		return utils.BadRequest(c, "双因素认证未启用", nil)
	}

	// 验证 TOTP 代码
	valid := totp.Validate(disableDTO.Code, user.TwoFactorSecret)
	if !valid {
		return utils.BadRequest(c, "验证码错误，请重试", nil)
	}

	// 禁用 2FA
	user.TwoFactorEnabled = false
	user.TwoFactorSecret = ""
	user.TwoFactorSetupAt = nil

	if err := database.DB.Save(&user).Error; err != nil {
		log.Printf("保存用户 2FA 设置失败: %v", err)
		return utils.ServerError(c, "禁用双因素认证失败", err)
	}

	log.Printf("User '%s' (ID=%d) disabled 2FA", user.Username, user.ID)

	return utils.Success(c, "双因素认证已禁用", nil)
}

// GetTwoFactorStatus 获取 2FA 状态
// @Summary 获取双因素认证状态
// @Description 获取当前用户的双因素认证状态
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} dto.StandardResponse{data=dto.TwoFactorStatusResponse} "成功响应"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Router /admin/auth/2fa/status [get]
func GetTwoFactorStatus(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	return utils.Success(c, "获取 2FA 状态成功", dto.TwoFactorStatusResponse{
		Enabled:   user.TwoFactorEnabled,
		SetupAt:   user.TwoFactorSetupAt,
		IsRequired: !user.TwoFactorEnabled && user.TwoFactorSetupAt == nil && user.LastedAt == nil,
	})
}

// ResetTwoFactor 重置用户2FA状态（仅用于开发测试）
func ResetTwoFactor(c *fiber.Ctx) error {
	var req struct {
		Username string `json:"username"`
	}

	if err := c.BodyParser(&req); err != nil {
		return utils.BadRequest(c, "请求参数错误", err)
	}

	if req.Username == "" {
		return utils.BadRequest(c, "用户名不能为空", nil)
	} 

	// 查找用户
	var user models.AdminUser
	if err := database.DB.Where("username = ?", req.Username).First(&user).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 重置2FA状态
	user.TwoFactorEnabled = false
	user.TwoFactorSecret = ""
	user.TwoFactorSetupAt = nil

	if err := database.DB.Save(&user).Error; err != nil {
		return utils.ServerError(c, "重置2FA状态失败", err)
	}

	log.Printf("用户 %s (ID=%d) 的2FA状态已重置", user.Username, user.ID)

	return utils.Success(c, "2FA状态重置成功", fiber.Map{
		"username": user.Username,
		"reset_at": time.Now(),
	})
}

// QuickEnableTwoFactor 快速启用 2FA（仅用于开发测试）
// @Summary 快速启用双因素认证（测试用）
// @Description 为当前用户快速启用双因素认证，使用固定的测试密钥
// @Tags Two Factor Authentication
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} dto.StandardResponse "启用成功"
// @Failure 401 {object} dto.StandardResponse "未登录"
// @Failure 500 {object} dto.StandardResponse "服务器错误"
// @Router /admin/auth/2fa/quick-enable [post]
func QuickEnableTwoFactor(c *fiber.Ctx) error {
	// 获取当前用户ID
	userID := utils.GetUserIDFromContext(c)
	if userID == 0 {
		return utils.Unauthorized(c, "未登录或登录已过期", nil)
	}

	// 查找用户
	var user models.AdminUser
	if err := database.DB.First(&user, userID).Error; err != nil {
		return utils.NotFound(c, "用户不存在")
	}

	// 使用固定的测试密钥（在生产环境中绝对不要这样做！）
	testSecret := "JBSWY3DPEHPK3PXP" // 这是一个标准的测试密钥

	// 启用 2FA
	now := time.Now()
	user.TwoFactorEnabled = true
	user.TwoFactorSecret = testSecret
	user.TwoFactorSetupAt = &now

	if err := database.DB.Save(&user).Error; err != nil {
		log.Printf("保存用户 2FA 设置失败: %v", err)
		return utils.ServerError(c, "启用双因素认证失败", err)
	}

	log.Printf("User '%s' (ID=%d) quick-enabled 2FA for testing", user.Username, user.ID)

	return utils.Success(c, "双因素认证快速启用成功", fiber.Map{
		"secret": testSecret,
		"note":   "请在 Google Authenticator 中手动添加此密钥：" + testSecret,
	})
}
