package utils

import (
	"go-fiber-api/database"
	"log"
	"time"

	"github.com/go-redis/redis/v8"
)

const (
	tempDataPrefix = "temp_data:"
)

// StoreTemporaryData 存储临时数据到 Redis
func StoreTemporaryData(key, value string, expiration time.Duration) error {
	redisKey := tempDataPrefix + key
	err := database.Rdb.Set(database.RedisCtx, redisKey, value, expiration).Err()
	if err != nil {
		log.Printf("存储临时数据失败 - Key: %s, Error: %v", key, err)
		return err
	}
	log.Printf("临时数据存储成功 - Key: %s, 过期时间: %v", key, expiration)
	return nil
}

// RetrieveTemporaryData 从 Redis 获取临时数据
func RetrieveTemporaryData(key string) (string, bool) {
	redisKey := tempDataPrefix + key
	value, err := database.Rdb.Get(database.RedisCtx, redisKey).Result()
	
	if err == redis.Nil {
		log.Printf("临时数据不存在 - Key: %s", key)
		return "", false // Key 不存在
	} else if err != nil {
		log.Printf("获取临时数据失败 - Key: %s, Error: %v", key, err)
		return "", false // 获取失败
	}
	
	log.Printf("临时数据获取成功 - Key: %s", key)
	return value, true
}

// ClearTemporaryData 清除 Redis 中的临时数据
func ClearTemporaryData(key string) error {
	redisKey := tempDataPrefix + key
	err := database.Rdb.Del(database.RedisCtx, redisKey).Err()
	if err != nil {
		log.Printf("清除临时数据失败 - Key: %s, Error: %v", key, err)
		return err
	}
	log.Printf("临时数据清除成功 - Key: %s", key)
	return nil
}

// CheckTemporaryDataExists 检查临时数据是否存在
func CheckTemporaryDataExists(key string) bool {
	redisKey := tempDataPrefix + key
	exists, err := database.Rdb.Exists(database.RedisCtx, redisKey).Result()
	if err != nil {
		log.Printf("检查临时数据存在性失败 - Key: %s, Error: %v", key, err)
		return false
	}
	return exists > 0
}

// GetTemporaryDataTTL 获取临时数据的剩余过期时间
func GetTemporaryDataTTL(key string) time.Duration {
	redisKey := tempDataPrefix + key
	ttl, err := database.Rdb.TTL(database.RedisCtx, redisKey).Result()
	if err != nil {
		log.Printf("获取临时数据TTL失败 - Key: %s, Error: %v", key, err)
		return -1
	}
	return ttl
}
