<template>
  <div class="login-container">
    <!-- 波浪背景效果 -->
    <div class="wave-container">
      <div class="wave wave1"></div>
      <div class="wave wave2"></div>
      <div class="wave wave3"></div>
    </div>

    <div class="login-content">
      <!-- 左侧品牌展示区 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo-container">
            <img v-if="siteLogo" :src="siteLogo" alt="网站Logo" class="site-logo" @error="handleLogoError" />
            <h1 v-else class="brand-title">XSS</h1>
          </div>
          <h2 class="brand-subtitle">安全测试平台</h2>
          <p class="brand-description" v-if="siteDescription">{{ siteDescription }}</p>
          <p class="brand-description" v-else>欢迎使用我们的专业安全测试平台，为您的应用提供全面保护。</p>
          
          <div class="features">
            <div class="feature-item">
              <i class="el-icon-lock"></i>
              <span>专业安全测试</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-monitor"></i>
              <span>实时监控分析</span>
            </div>
            <div class="feature-item">
              <i class="el-icon-data-line"></i>
              <span>全面报告生成</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单区 -->
      <div class="form-section">
        <!-- 维护模式提示 -->
        <el-alert
          v-if="isSiteClosed"
          type="warning"
          :closable="false"
          class="maintenance-alert"
          show-icon
        >
          <div class="maintenance-content">
            <h3>系统维护中</h3>
            <div v-html="closedMessage"></div>
            <div class="admin-login-hint" @click="toggleAdminLogin">
              管理员登录 <i :class="showAdminLogin ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"></i>
            </div>
          </div>
        </el-alert>
        
        <div class="form-header">
          <h2 v-if="!isSiteClosed || (isSiteClosed && !showAdminLogin)">欢迎回来</h2>
          <h2 v-else>管理员登录</h2>
          <!-- <p v-if="!isSiteClosed">请按顺序完成三步验证：图形验证码 → Google验证码 → 用户名密码</p> -->
          <!-- <p v-else>管理员登录需要完成三步验证：图形验证码 → Google验证码 → 用户名密码</p> -->
        </div>

        <!-- 管理员登录表单 - 在维护模式下显示 -->
        <div class="form-container" v-if="isSiteClosed && showAdminLogin">
          <el-form
            :model="loginForm"
            :rules="dynamicRules"
            ref="loginForm"
            label-width="0"
            @submit.native.prevent
          >
            <!-- 用户名 -->
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="管理员用户名"
                prefix-icon="el-icon-user"
              >
              </el-input>
            </el-form-item>

            <!-- 密码 -->
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="管理员密码"
                show-password
                prefix-icon="el-icon-lock"
              >
              </el-input>
            </el-form-item>

            <!-- 验证码 -->
            <el-form-item prop="captcha">
              <div class="captcha-container">
                <el-input
                  v-model="loginForm.captcha"
                  placeholder="验证码"
                  prefix-icon="el-icon-key"
                ></el-input>
                <div class="captcha-image" @click="refreshCaptcha">
                  <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                  <div v-else class="captcha-loading">
                    <i class="el-icon-loading"></i>
                  </div>
                </div>
              </div>
            </el-form-item>

            <!-- Google Authenticator 验证码 -->
            <el-form-item prop="two_factor_code">
              <el-input
                v-model="loginForm.two_factor_code"
                placeholder="Google Authenticator 验证码"
                prefix-icon="el-icon-lock"
                maxlength="6"
                show-word-limit
                clearable
              >
                <template slot="prepend">2FA</template>
              </el-input>
              <div class="form-help-text">
                <i class="el-icon-info"></i>
                请打开 Google Authenticator 应用获取6位验证码
              </div>
            </el-form-item>

            <!-- 登录按钮 -->
            <el-form-item>
              <el-button
                type="danger"
                :loading="loading"
                class="submit-button admin-button"
                @click="handleAdminLogin"
              >
                管理员登录 <i class="el-icon-right"></i>
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 普通登录表单 - 仅在非维护模式下显示 -->
        <div class="form-container" v-if="!isSiteClosed">
          <!-- 验证步骤说明 -->
          <!-- <div class="verification-steps">
            <el-alert
              title="登录验证步骤"
              type="info"
              :closable="false"
              show-icon
            >
              <template slot="default">
                <ol class="steps-list">
                  <li>第一步：输入图形验证码</li>
                  <li>第二步：输入Google Authenticator验证码</li>
                  <li>第三步：输入用户名和密码</li>
                </ol>
                <p class="steps-note">请按顺序完成所有验证步骤</p>
              </template>
            </el-alert>
          </div> -->

          <el-form
            :model="loginForm"
            :rules="dynamicRules"
            ref="loginForm"
            label-width="0"
            @submit.native.prevent
          >
            <!-- 用户名 -->
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="用户名"
                prefix-icon="el-icon-user"
              >
              </el-input>
            </el-form-item>

            <!-- 密码 -->
            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="密码"
                show-password
                prefix-icon="el-icon-lock"
              >
              </el-input>
            </el-form-item>

            <!-- 验证码 -->
            <el-form-item prop="captcha">
              <div class="captcha-container">
                <el-input
                  v-model="loginForm.captcha"
                  placeholder="验证码"
                  prefix-icon="el-icon-key"
                ></el-input>
                <div class="captcha-image" @click="refreshCaptcha">
                  <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                  <div v-else class="captcha-loading">
                    <i class="el-icon-loading"></i>
                  </div>
                </div>
              </div>
            </el-form-item>



            <!-- Google Authenticator 验证码 -->
            <el-form-item prop="two_factor_code">
              <el-input
                v-model="loginForm.two_factor_code"
                placeholder="Google Authenticator 验证码（可选）"
                prefix-icon="el-icon-lock"
                maxlength="6"
                show-word-limit
                clearable
              >
                <template slot="prepend">2FA</template>
              </el-input>

              <!-- 首次设置2FA提示和按钮 -->
              <div v-if="needsSetup2FA && !show2FAQRCode" class="setup-2fa-prompt">
                <div class="setup-info">
                  <i class="el-icon-warning-outline"></i>
                  <span>首次登录需要设置双因素认证以确保账户安全</span>
                </div>
                <el-button
                  type="primary"
                  size="small"
                  @click="generate2FAQRCode"
                  :loading="qrCodeLoading"
                  icon="el-icon-mobile-phone"
                >
                  设置Google Authenticator
                </el-button>
              </div>

              <!-- 2FA二维码显示区域 -->
              <div class="two-factor-qr-section" v-if="show2FAQRCode">
                <div class="qr-code-container">
                  <div v-if="qrCodeUrl" class="qr-code-display">
                    <img :src="qrCodeUrl" alt="Google Authenticator 二维码" class="qr-code-image" />
                    <div class="qr-code-info">
                      <p class="qr-title">设置Google Authenticator</p>
                      <p class="qr-instruction">请使用Google Authenticator扫描此二维码</p>
                      <div class="manual-key" v-if="secretKey">
                        <p class="manual-title">或手动输入密钥：</p>
                        <el-input
                          :value="secretKey"
                          readonly
                          size="small"
                          class="secret-input"
                        >
                          <el-button
                            slot="append"
                            icon="el-icon-document-copy"
                            @click="copySecret"
                            size="small"
                          >
                            复制
                          </el-button>
                        </el-input>
                      </div>
                      <div class="setup-actions">
                        <el-button size="small" @click="cancelSetup2FA">取消设置</el-button>
                      </div>
                    </div>
                  </div>
                  <div v-else-if="qrCodeLoading" class="qr-loading">
                    <i class="el-icon-loading"></i>
                    <p>正在生成二维码...</p>
                  </div>
                  <div v-else class="qr-error">
                    <i class="el-icon-warning"></i>
                    <p>二维码生成失败</p>
                    <el-button size="small" @click="generate2FAQRCode">重新生成</el-button>
                  </div>
                </div>
              </div>

              <div class="form-help-text">
                <i class="el-icon-info"></i>
                <span v-if="show2FAQRCode">扫描上方二维码后，输入Google Authenticator中显示的6位验证码</span>
                <span v-else>如需设置双因素认证，请点击上方按钮显示二维码</span>
              </div>
            </el-form-item>

            <!-- 记住我 -->
            <el-form-item>
              <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            </el-form-item>

            <!-- 登录按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                :loading="loading"
                class="submit-button"
                @click="show2FAQRCode ? verify2FAAndLogin() : handleLogin()"
              >
                {{ show2FAQRCode ? '完成2FA设置' : '登录' }} <i class="el-icon-right"></i>
              </el-button>
            </el-form-item>

            <!-- 额外链接 -->
            <div class="form-links">
              <router-link v-if="!isRegisterClosed" to="/signup" class="link">
                <i class="el-icon-plus"></i> 注册账号
              </router-link>
              <router-link to="/forgot-password" class="link">
                <i class="el-icon-question"></i> 忘记密码
              </router-link>
            </div>
          </el-form>
        </div>

        <!-- 自定义页脚信息 -->
        <div class="copyright" v-if="siteFooter" v-html="siteFooter"></div>
        <div class="copyright" v-else>
          © {{ new Date().getFullYear() }} XSS测试平台
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { getCaptcha } from "@/api/auth";
import { getPublicSettings } from "@/api/settings";
import { generateTwoFactorSecret, verifyTwoFactorSetup } from "@/api/auth";
export default {
  name: "Login",
  data() {
    return {
      loading: false,
      captchaImage: "",
      captchaKey: "",
      rememberMe: false,
      isRegisterClosed: false, // 是否关闭注册
      isSiteClosed: false, // 网站是否处于维护模式
      closedMessage: "网站正在维护中，请稍后再试。", // 维护模式提示信息
      siteDescription: "", // 网站描述
      siteFooter: "", // 页脚信息
      siteLogo: "", // 网站Logo
      loginForm: {
        username: "",
        password: "",
        captcha: "",
        captcha_key: "",

        two_factor_code: "", // 双因素认证代码
      },
      showAdminLogin: false,
      // 2FA相关数据
      needsSetup2FA: false,      // 是否需要设置2FA
      show2FAQRCode: false,      // 是否显示二维码
      qrCodeUrl: '',
      secretKey: '',
      qrCodeLoading: false,
    };
  },
  computed: {
    // 动态2FA验证规则
    dynamicRules() {
      const baseRules = {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          {
            min: 3,
            max: 20,
            message: "长度在 3 到 20 个字符",
            trigger: "blur",
          },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            min: 6,
            max: 20,
            message: "长度在 6 到 20 个字符",
            trigger: "blur",
          },
        ],
        captcha: [{ required: true, message: "请输入验证码", trigger: "blur" }],
        two_factor_code: []
      };

      // 如果显示2FA二维码（首次设置）或者用户已启用2FA，则要求输入验证码
      // 根据是否显示二维码设置验证规则
      if (this.show2FAQRCode) {
        // 显示二维码时，要求输入验证码完成设置
        baseRules.two_factor_code = [
          { required: true, message: "请输入Google Authenticator验证码完成设置", trigger: "blur" },
          { min: 6, max: 6, message: "验证码必须是6位数字", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "验证码只能包含数字", trigger: "blur" }
        ];
      } else {
        // 不显示二维码时，验证码为可选
        baseRules.two_factor_code = [
          { min: 6, max: 6, message: "验证码必须是6位数字", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "验证码只能包含数字", trigger: "blur" }
        ];
      }

      return baseRules;
    }
  },
  created() {
    // 获取公开设置
    this.loadPublicSettings().then(() => {
      // 在非维护模式下获取验证码，或者在维护模式下但显示管理员登录表单时获取验证码
      if (!this.isSiteClosed || this.showAdminLogin) {
        this.refreshCaptcha();
      }
    });

    // 如果存在记住我标记，则设置rememberMe
    if (localStorage.getItem("remember_me") === "1") {
      this.rememberMe = true;
    }

    // 如果已经登录，重定向到首页
    if (this.$store.getters.isLoggedIn) {
      const redirect = this.$route.query.redirect || "/";
      this.$router.push(redirect);
    }
  },

  mounted() {
    // 窗口大小改变时重新调整布局
    window.addEventListener("resize", this.handleResize);
    this.handleResize();
  },
  beforeDestroy() {
    // 清除事件监听
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    ...mapActions(["login", "logout"]),
    
    // 处理窗口大小改变
    handleResize() {
      // 可以根据窗口大小调整UI
    },
    
    // 加载公开设置
    loadPublicSettings() {
      return getPublicSettings()
        .then((response) => {
          // 检查网站是否处于维护模式 - 严格检查字符串值
          this.isSiteClosed = response.site_closed === "true";
          
          // 如果处于维护模式，设置维护信息
          if (this.isSiteClosed && response.closed_message) {
            this.closedMessage = response.closed_message;
          }
          
          // 设置其他公开设置
          this.isRegisterClosed = response.register_closed === "true";
          
          // 设置网站描述
          if (response.site_description) {
            this.siteDescription = response.site_description;
          }
          
          // 设置页脚信息
          if (response.site_footer) {
            this.siteFooter = response.site_footer;
          }
          
          // 设置网站Logo
          if (response.site_logo) {
            // 如果是相对路径，添加基础URL
            if (response.site_logo.startsWith('/')) {
              this.siteLogo = `http://**************:3000${response.site_logo}`;
            } else {
              this.siteLogo = response.site_logo;
            }
          }
          
          return response;
        })
        .catch((error) => {
          console.error("获取公开设置失败:", error);
          // 静默失败，使用默认值
          this.isRegisterClosed = false;
          this.isSiteClosed = false;
          return Promise.resolve(); // 确保链式调用可以继续
        });
    },
    
    // 处理Logo加载失败
    handleLogoError() {
      console.log("Logo加载失败，使用文本标题替代");
      this.siteLogo = ""; // 清空Logo URL，触发显示文本标题
    },







    // 刷新验证码
    refreshCaptcha() {
      // 在维护模式下，只有显示管理员登录表单时才获取验证码
      if (this.isSiteClosed && !this.showAdminLogin) {
        return;
      }
      
      this.loading = true;
      getCaptcha()
        .then((res) => {
          this.captchaImage = res.image_data;
          this.captchaKey = res.captcha_key;
          this.loginForm.captcha_key = res.captcha_key;
        })
        .catch(() => {
          this.$message.error("获取验证码失败，请重试");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理登录逻辑
    handleLogin() {
      // 如果网站处于维护模式，阻止登录
      if (this.isSiteClosed) {
        this.$message.error("网站维护中，暂时无法登录");
        return;
      }
      
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 添加记住我参数到登录数据中
          const loginData = {
            ...this.loginForm,
            remember: this.rememberMe ? 1 : 0,
          };

          this.login(loginData)
            .then((response) => {
              // 检查是否需要设置2FA（首次登录）
              if (response && response.requires_2fa_setup) {
                this.$message.warning("首次登录需要设置双因素认证");
                // 保存临时token
                localStorage.setItem('temp_token', response.temp_token);
                // 设置需要2FA设置状态
                this.needsSetup2FA = true;
                // 自动显示二维码
                this.generate2FAQRCode();
                return;
              }

              this.$message.success("登录成功");

              // 如果选择了记住我，设置本地存储标记
              if (this.rememberMe) {
                localStorage.setItem("remember_me", "1");
              } else {
                localStorage.removeItem("remember_me");
              }

              // 如果有重定向地址，则跳转到该地址
              const redirectUrl = this.$route.query.redirect || "/";
              this.$router.push(redirectUrl);
            })
            .catch((error) => {
              // 提取更详细的错误信息
              let errorMsg = "登录失败，请重试";
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg;

                // 根据错误类型提供更具体的提示
                if (errorMsg.includes("图形验证码")) {
                  this.$message.error("第一步验证失败：图形验证码错误，请重新输入");
                } else if (errorMsg.includes("Google Authenticator") || errorMsg.includes("TOTP") || errorMsg.includes("双因素认证")) {
                  this.$message.error("第二步验证失败：Google Authenticator验证码错误，请重新输入");
                  // 清空2FA输入框
                  this.loginForm.two_factor_code = "";
                } else if (errorMsg.includes("请输入Google Authenticator验证码")) {
                  this.$message.error("第二步验证失败：请输入Google Authenticator验证码");
                } else if (errorMsg.includes("用户名或密码")) {
                  this.$message.error("第三步验证失败：用户名或密码错误，请检查后重试");
                } else if (errorMsg.includes("账户已被禁用")) {
                  this.$message.error("第三步验证失败：账户已被禁用，请联系管理员");
                } else {
                  this.$message.error(errorMsg);
                }
              } else if (error.message) {
                this.$message.error("网络连接失败，请检查网络后重试");
              } else {
                this.$message.error(errorMsg);
              }

              // 刷新验证码
              this.refreshCaptcha();
              this.loginForm.captcha = "";
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 处理管理员登录逻辑
    handleAdminLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 添加记住我参数到登录数据中
          const loginData = {
            ...this.loginForm,
            remember: this.rememberMe ? 1 : 0,
            is_admin_login: true // 标记为管理员登录
          };

          this.login(loginData)
            .then((response) => {
              // 检查是否需要设置 2FA
              if (response && response.requires_2fa_setup) {
                this.$message.warning("首次登录需要设置双因素认证");
                // 保存临时 token 用于 2FA 设置
                localStorage.setItem("temp_token", response.temp_token);
                // 设置需要2FA设置状态
                this.needsSetup2FA = true;
                // 自动显示二维码
                this.generate2FAQRCode();
                return;
              }

              // 检查是否为管理员
              const isAdmin = response && response.user_info &&
                (response.user_info.is_admin === true || response.user_info.role === 1);

              if (isAdmin) {
                this.$message.success("管理员登录成功");

                // 如果选择了记住我，设置本地存储标记
                if (this.rememberMe) {
                  localStorage.setItem("remember_me", "1");
                } else {
                  localStorage.removeItem("remember_me");
                }

                // 如果有重定向地址，则跳转到该地址
                const redirectUrl = this.$route.query.redirect || "/";
                this.$router.push(redirectUrl);
              } else {
                // 如果不是管理员，注销并显示错误
                this.logout().then(() => {
                  this.$message.error("非管理员账号无法在维护模式下登录");
                  this.refreshCaptcha();
                  this.loginForm.captcha = "";
                });
              }
            })
            .catch((error) => {
              // 提取更详细的错误信息
              let errorMsg = "登录失败，请重试";
              if (error.response && error.response.data) {
                errorMsg = error.response.data.message || errorMsg;

                // 根据错误类型提供更具体的提示
                if (errorMsg.includes("图形验证码")) {
                  this.$message.error("第一步验证失败：图形验证码错误，请重新输入");
                } else if (errorMsg.includes("Google Authenticator") || errorMsg.includes("TOTP") || errorMsg.includes("双因素认证")) {
                  this.$message.error("第二步验证失败：Google Authenticator验证码错误，请重新输入");
                  // 清空2FA输入框
                  this.loginForm.two_factor_code = "";
                } else if (errorMsg.includes("请输入Google Authenticator验证码")) {
                  this.$message.error("第二步验证失败：请输入Google Authenticator验证码");
                } else if (errorMsg.includes("用户名或密码")) {
                  this.$message.error("第三步验证失败：用户名或密码错误，请检查后重试");
                } else if (errorMsg.includes("账户已被禁用")) {
                  this.$message.error("第三步验证失败：账户已被禁用，请联系管理员");
                } else {
                  this.$message.error(errorMsg);
                }
              } else if (error.message) {
                this.$message.error("网络连接失败，请检查网络后重试");
              } else {
                this.$message.error(errorMsg);
              }

              // 刷新验证码
              this.refreshCaptcha();
              this.loginForm.captcha = "";
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },

    // 切换管理员登录表单的显示状态
    toggleAdminLogin() {
      this.showAdminLogin = !this.showAdminLogin;
      if (this.showAdminLogin) {
        this.refreshCaptcha();
      }
    },

    // 生成2FA二维码
    async generate2FAQRCode() {
      this.qrCodeLoading = true;
      try {
        const response = await generateTwoFactorSecret();
        console.log('2FA生成响应:', response);

        // 由于响应拦截器已经返回了data，所以直接访问response
        if (response && response.qr_code_url) {
          this.qrCodeUrl = response.qr_code_url;
          this.secretKey = response.secret;
          this.show2FAQRCode = true;
          this.$message.success('二维码生成成功，请扫描设置Google Authenticator');
          console.log('二维码URL:', this.qrCodeUrl);
          console.log('密钥:', this.secretKey);
          console.log('显示状态:', this.show2FAQRCode);
        } else {
          console.error('响应数据格式错误:', response);
          this.$message.error('生成二维码失败：响应数据格式错误');
        }
      } catch (error) {
        console.error('生成2FA二维码失败:', error);
        this.$message.error('生成二维码失败，请重试');
      } finally {
        this.qrCodeLoading = false;
      }
    },

    // 复制密钥
    copySecret() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.secretKey).then(() => {
          this.$message.success('密钥已复制到剪贴板');
        }).catch(() => {
          this.fallbackCopySecret();
        });
      } else {
        this.fallbackCopySecret();
      }
    },

    // 备用复制方法
    fallbackCopySecret() {
      const textArea = document.createElement('textarea');
      textArea.value = this.secretKey;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        this.$message.success('密钥已复制到剪贴板');
      } catch (err) {
        this.$message.error('复制失败，请手动复制');
      }
      document.body.removeChild(textArea);
    },

    // 验证2FA并完成登录
    async verify2FAAndLogin() {
      if (!this.loginForm.two_factor_code) {
        this.$message.error('请输入Google Authenticator验证码');
        return;
      }

      this.loading = true;
      try {
        const response = await verifyTwoFactorSetup({
          code: this.loginForm.two_factor_code
        });

        if (response && response.token) {
          this.$message.success('2FA设置完成，登录成功！');

          // 清除临时token，保存正式token
          localStorage.removeItem('temp_token');
          localStorage.setItem('token', response.token);

          // 重置2FA相关状态
          this.show2FAQRCode = false;
          this.qrCodeUrl = '';
          this.secretKey = '';

          // 如果选择了记住我，设置本地存储标记
          if (this.rememberMe) {
            localStorage.setItem("remember_me", "1");
          } else {
            localStorage.removeItem("remember_me");
          }

          // 跳转到主页面
          const redirectUrl = this.$route.query.redirect || "/";
          this.$router.push(redirectUrl);
        } else {
          this.$message.error('验证码错误或2FA设置失败');
          this.loginForm.two_factor_code = '';
        }
      } catch (error) {
        console.error('2FA验证失败:', error);
        this.$message.error('验证失败，请重试');
        this.loginForm.two_factor_code = '';
      } finally {
        this.loading = false;
      }
    },

    // 取消2FA设置
    cancelSetup2FA() {
      this.show2FAQRCode = false;
      this.qrCodeUrl = '';
      this.secretKey = '';
      this.loginForm.two_factor_code = '';

      this.$message.info('已取消2FA设置');
    },

    // 检查是否需要显示2FA二维码
    checkNeed2FASetup(username) {
      // 这里可以根据用户名或其他条件判断是否需要显示2FA设置
      // 暂时简单处理：如果用户名不为空且没有2FA验证码，则显示二维码
      if (username && !this.loginForm.two_factor_code) {
        this.generate2FAQRCode();
      }
    },
  },
};
</script>

<style scoped>
.login-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 波浪背景效果 */
.wave-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background-repeat: repeat-x;
  background-position: 0 bottom;
  transform-origin: center bottom;
}

.wave1 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23a4dbff" fill-opacity="0.2" d="M0,192L48,197.3C96,203,192,213,288,229.3C384,245,480,267,576,261.3C672,256,768,224,864,213.3C960,203,1056,213,1152,224C1248,235,1344,245,1392,250.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  animation: wave 15s linear infinite;
  z-index: 1;
  opacity: 0.6;
  animation-delay: 0s;
  bottom: 0;
}

.wave2 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%238ed1fc" fill-opacity="0.3" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,106.7C672,117,768,171,864,181.3C960,192,1056,160,1152,149.3C1248,139,1344,149,1392,154.7L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  animation: wave 20s linear infinite;
  z-index: 2;
  opacity: 0.4;
  animation-delay: -5s;
  bottom: 0;
}

.wave3 {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%236cb2eb" fill-opacity="0.2" d="M0,256L48,229.3C96,203,192,149,288,154.7C384,160,480,224,576,234.7C672,245,768,203,864,181.3C960,160,1056,160,1152,154.7C1248,149,1344,139,1392,133.3L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
  animation: wave 30s linear infinite;
  z-index: 3;
  opacity: 0.3;
  animation-delay: -2s;
  bottom: 0;
}

@keyframes wave {
  0% {
    transform: translateX(0) translateZ(0) scaleY(1);
  }
  50% {
    transform: translateX(-25%) translateZ(0) scaleY(0.8);
  }
  100% {
    transform: translateX(-50%) translateZ(0) scaleY(1);
  }
}

.login-content {
  display: flex;
  width: 80%;
  max-width: 1200px;
  background-color: #ffffff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: relative;
}

/* 左侧品牌区 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #3f87a6, #4b6cb7);
  color: #fff;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.brand-content {
  max-width: 400px;
  text-align: center;
}

.brand-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  letter-spacing: 2px;
}

.brand-subtitle {
  font-size: 1.5rem;
  font-weight: 300;
  margin-bottom: 20px;
  letter-spacing: 1px;
}

.brand-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 30px;
  opacity: 0.9;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s;
}

.feature-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}

.feature-item i {
  font-size: 1.5rem;
}

.site-logo {
  max-width: 150px;
  max-height: 150px;
  margin-bottom: 20px;
  border-radius: 10px;
}

/* 右侧表单区 */
.form-section {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.form-header p {
  font-size: 1rem;
  color: #666;
}

.form-container {
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

/* 维护模式提示样式 */
.maintenance-alert {
  margin-bottom: 30px;
  border-radius: 10px;
  background-color: #fef8e7 !important;
  border-color: #faecd8 !important;
}

.maintenance-alert /deep/ .el-alert__content {
  padding: 15px;
}

.maintenance-content {
  text-align: left;
}

.maintenance-content h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.2rem;
  color: #e6a23c;
}

/* 表单元素样式 */
.form-container /deep/ .el-input__inner {
  border-radius: 8px;
  padding: 12px 15px;
  height: 48px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s;
}

.form-container /deep/ .el-input__inner:focus {
  border-color: #4b6cb7;
  box-shadow: 0 0 0 2px rgba(75, 108, 183, 0.1);
}

.form-container /deep/ .el-input__prefix {
  left: 15px;
}

.form-container /deep/ .el-input--prefix .el-input__inner {
  padding-left: 45px;
}

.captcha-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.captcha-image {
  width: 120px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  border: 1px solid #e0e0e0;
}

.captcha-image img {
  max-width: 100%;
  max-height: 100%;
}

.captcha-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}



.form-container /deep/ .el-checkbox__label {
  color: #666;
}

/* 按钮样式 */
.submit-button {
  width: 100%;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 1px;
  height: auto;
  transition: all 0.3s;
}

.submit-button.el-button--primary {
  background: linear-gradient(to right, #4b6cb7, #3f87a6);
  border: none;
}

.submit-button.el-button--primary:hover {
  background: linear-gradient(to right, #3f57a3, #2d7491);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(75, 108, 183, 0.3);
}

.submit-button.admin-button {
  background: linear-gradient(to right, #f5af19, #f12711);
  border: none;
}

.submit-button.admin-button:hover {
  background: linear-gradient(to right, #e59d0e, #db2008);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(241, 39, 17, 0.3);
}

/* 2FA 相关样式 */
.form-help-text {
  margin-top: 8px;
  font-size: 0.85rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 5px;
}

.form-help-text i {
  color: #409eff;
}

/* 2FA 输入框特殊样式 */
.form-container /deep/ .el-input-group__prepend {
  background: linear-gradient(to right, #4b6cb7, #3f87a6);
  color: white;
  border: none;
  font-weight: 600;
  letter-spacing: 1px;
}

/* 验证步骤说明样式 */
.verification-steps {
  margin-bottom: 20px;
}

.verification-steps .el-alert {
  border-radius: 8px;
  border: none;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
}

.steps-list {
  margin: 10px 0;
  padding-left: 20px;
  color: #1976d2;
}

.steps-list li {
  margin: 8px 0;
  font-weight: 500;
}

.steps-note {
  margin: 10px 0 0 0;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

/* 链接样式 */
.form-links {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.link {
  color: #4b6cb7;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.link:hover {
  color: #3f57a3;
  text-decoration: underline;
}

.admin-login-hint {
  color: #e6a23c;
  cursor: pointer;
  margin-top: 10px;
  font-size: 0.9rem;
  display: inline-block;
  transition: all 0.3s;
}

.admin-login-hint:hover {
  color: #d48806;
}

.copyright {
  margin-top: 40px;
  text-align: center;
  color: #999;
  font-size: 0.85rem;
}

.copyright /deep/ a {
  color: #4b6cb7;
  text-decoration: none;
}

.copyright /deep/ a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .login-content {
    flex-direction: column;
    width: 90%;
  }
  
  .brand-section {
    padding: 30px 20px;
  }
  
  .form-section {
    padding: 30px 20px;
  }
}

@media (max-width: 576px) {
  .login-content {
    width: 95%;
  }
  
  .brand-section {
    padding: 20px 15px;
  }
  
  .brand-title {
    font-size: 2.5rem;
  }
  
  .form-section {
    padding: 20px 15px;
  }
  
  .form-header h2 {
    font-size: 1.5rem;
  }
}

/* 2FA设置提示样式 */
.setup-2fa-prompt {
  margin-top: 10px;
  padding: 15px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  text-align: center;
}

.setup-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  color: #d48806;
  font-size: 14px;
}

.setup-info i {
  margin-right: 8px;
  font-size: 16px;
}

/* 2FA二维码样式 */
.two-factor-qr-section {
  margin-top: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.qr-code-container {
  text-align: center;
}

.qr-code-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: white;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code-info {
  max-width: 300px;
}

.qr-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.qr-instruction {
  font-size: 14px;
  color: #666;
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.manual-key {
  margin-top: 15px;
}

.manual-title {
  font-size: 13px;
  color: #666;
  margin: 0 0 8px 0;
}

.secret-input {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.setup-actions {
  margin-top: 15px;
  text-align: center;
}

.qr-loading {
  padding: 40px 20px;
  color: #666;
}

.qr-loading i {
  font-size: 24px;
  margin-bottom: 10px;
  color: #409eff;
}

.qr-loading p {
  margin: 0;
  font-size: 14px;
}

.qr-error {
  padding: 40px 20px;
  color: #f56c6c;
}

.qr-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.qr-error p {
  margin: 0 0 15px 0;
  font-size: 14px;
}

@media (max-width: 768px) {
  .two-factor-qr-section {
    margin-top: 10px;
    padding: 15px;
  }

  .qr-code-image {
    width: 160px;
    height: 160px;
  }

  .qr-code-info {
    max-width: 250px;
  }
}
</style>
