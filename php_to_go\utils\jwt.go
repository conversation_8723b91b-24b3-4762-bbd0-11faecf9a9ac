// utils/jwt.go
package utils

import (
	"fmt"
	"log"
	"go-fiber-api/config"
	"go-fiber-api/database"
	"go-fiber-api/models"
	"time"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"
)

// JWTClaims 定义JWT中存储的内容
type JWTClaims struct {
	ID       uint64  `json:"id"`
	Username string  `json:"username"`
	Name     string  `json:"name"`
	jwt.RegisteredClaims
}

// AuthClaims 定义自定义JWT中存储的内容，用于密码重置等特殊场景
type AuthClaims struct {
	UserID    uint64 `json:"user_id"`
	TokenType string `json:"token_type"` // 标识令牌的用途，如 "password_reset"
	ExpiresAt int64  `json:"exp"`
}

// GenerateToken 生成JWT token
func GenerateToken(userID uint64, username, name string) (string, error) {
	cfg := config.LoadConfig()
	expireTime := time.Now().Add(24 * time.Hour) // 默认24小时过期

	claims := JWTClaims{
		ID:       userID,
		Username: username,
		Name:     name,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "go-fiber-api",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(cfg.JWTSecret))
	return tokenString, err
}

// GenerateJWT 生成带有自定义过期时间的JWT token
func GenerateJWT(userID uint64, username string, isAdmin bool, duration time.Duration) (string, error) {
	cfg := config.LoadConfig()
	expireTime := time.Now().Add(duration)

	// 创建自定义 claims，包含临时标识
	claims := jwt.MapClaims{
		"id":       userID,
		"username": username,
		"name":     username, // 默认使用 username 作为 name
		"is_admin": isAdmin,
		"temp":     true, // 标识这是临时 token
		"exp":      expireTime.Unix(),
		"iat":      time.Now().Unix(),
		"nbf":      time.Now().Unix(),
		"iss":      "go-fiber-api",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(cfg.JWTSecret))
	return tokenString, err
}

// GenerateCustomToken 生成自定义JWT token，用于特殊场景如密码重置
func GenerateCustomToken(claims AuthClaims) (string, error) {
	cfg := config.LoadConfig()
	
	// 创建一个新的JWT对象
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"user_id":    claims.UserID,
		"token_type": claims.TokenType,
		"exp":        claims.ExpiresAt,
	})
	
	// 签名并获取完整的编码后的字符串令牌
	tokenString, err := token.SignedString([]byte(cfg.JWTSecret))
	return tokenString, err
}

// ParseCustomToken 解析自定义JWT token
func ParseCustomToken(tokenString string) (*AuthClaims, error) {
	cfg := config.LoadConfig()
	
	// 解析token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return []byte(cfg.JWTSecret), nil
	})
	
	if err != nil {
		return nil, err
	}
	
	// 验证token并提取claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 提取字段
		userID, _ := claims["user_id"].(float64)
		tokenType, _ := claims["token_type"].(string)
		exp, _ := claims["exp"].(float64)
		
		return &AuthClaims{
			UserID:    uint64(userID),
			TokenType: tokenType,
			ExpiresAt: int64(exp),
		}, nil
	}
	
	return nil, jwt.ErrSignatureInvalid
}

// GetUserIDFromContext 从Fiber Context获取用户ID
func GetUserIDFromContext(c *fiber.Ctx) uint64 {
	// 从token中提取
	// 从Authorization头提取token
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		log.Println("GetUserIDFromContext: 缺少Authorization头")
		return 0
	}

	// 检查是否为 Bearer Token
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		log.Println("GetUserIDFromContext: 令牌格式无效")
		return 0
	}
	tokenString := parts[1]

	// 首先尝试解析为标准 token
	claims, err := ParseToken(tokenString)
	if err == nil {
		log.Printf("GetUserIDFromContext: 从标准token获取到用户ID: %d", claims.ID)
		return claims.ID
	}

	// 如果标准解析失败，尝试解析为临时 token
	tempClaims, err := ParseTempToken(tokenString)
	if err != nil {
		log.Println("GetUserIDFromContext: 解析token失败:", err)
		return 0
	}

	log.Printf("GetUserIDFromContext: 从临时token获取到用户ID: %d", tempClaims.ID)
	return tempClaims.ID
}

// GetUsernameFromContext 从Fiber Context获取用户名
func GetUsernameFromContext(c *fiber.Ctx) string {
	// 从token中提取
	// 从Authorization头提取token
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		log.Println("GetUsernameFromContext: 缺少Authorization头")
		return ""
	}
	
	// 检查是否为 Bearer Token
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != "Bearer" {
		log.Println("GetUsernameFromContext: 令牌格式无效")
		return ""
	}
	tokenString := parts[1]
	
	// 解析token
	claims, err := ParseToken(tokenString)
	if err != nil {
		log.Println("GetUsernameFromContext: 解析token失败:", err)
		return ""
	}
	
	return claims.Username
}

// IsAuthenticated 检查是否已认证
func IsAuthenticated(c *fiber.Ctx) bool {
	// 直接调用GetUserIDFromContext，如果有有效的JWT令牌，会返回用户ID
	return GetUserIDFromContext(c) > 0
}

// RequireAuthentication 强制要求认证的中间件
func RequireAuthentication(c *fiber.Ctx) error {
	if !IsAuthenticated(c) {
		log.Println("RequireAuthentication: 用户未认证")
		return Unauthorized(c, "需要认证", nil)
	}
	
	// 认证通过
	userID := GetUserIDFromContext(c)
	log.Printf("RequireAuthentication: 用户 %d 已认证通过", userID)
	return c.Next()
}

// ParseToken 解析JWT token并返回解析后的Claims
func ParseToken(tokenString string) (*JWTClaims, error) {
	log.Println("解析token:", tokenString)
	cfg := config.LoadConfig()
	jwtSecret := []byte(cfg.JWTSecret)

	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法是否为HMAC
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	// 验证token并提取claims
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		log.Println("解析claims成功:", claims)
		return claims, nil
	}

	return nil, jwt.ErrSignatureInvalid
}

// ParseTempToken 解析临时JWT token并返回解析后的Claims
func ParseTempToken(tokenString string) (*JWTClaims, error) {
	log.Println("解析临时token:", tokenString)
	cfg := config.LoadConfig()
	jwtSecret := []byte(cfg.JWTSecret)

	// 解析token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法是否为HMAC
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, jwt.ErrSignatureInvalid
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	// 验证token并提取claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// 检查是否为临时 token
		if temp, exists := claims["temp"]; !exists || temp != true {
			return nil, fmt.Errorf("不是临时token")
		}

		// 提取字段并转换为 JWTClaims 结构
		id, _ := claims["id"].(float64)
		username, _ := claims["username"].(string)
		name, _ := claims["name"].(string)

		jwtClaims := &JWTClaims{
			ID:       uint64(id),
			Username: username,
			Name:     name,
		}

		log.Println("解析临时token成功:", jwtClaims)
		return jwtClaims, nil
	}

	return nil, jwt.ErrSignatureInvalid
}

// GetUserDetailsFromToken 从token字符串中提取用户详情
func GetUserDetailsFromToken(tokenString string) (uint64, string, string, error) {
	claims, err := ParseToken(tokenString)
	if err != nil {
		return 0, "", "", err
	}
	
	return claims.ID, claims.Username, claims.Name, nil
}

// IsAdmin 检查当前用户是否为管理员
func IsAdmin(c *fiber.Ctx) bool {
	// 首先确认用户已认证
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		log.Println("IsAdmin: 用户未认证")
		return false
	}
	
	log.Printf("IsAdmin: 检查用户ID=%d是否为管理员", userID)
	
	// 检查是否是超级管理员 (ID=1)
	if userID == 1 {
		log.Printf("IsAdmin: 用户ID=1，自动判定为超级管理员")
		return true
	}
	
	// 查询用户角色
	var user models.AdminUser
	if err := database.DB.Preload("Roles").First(&user, userID).Error; err != nil {
		log.Printf("IsAdmin: 查询用户数据失败: %v", err)
		return false
	}
	
	log.Printf("IsAdmin: 用户名=%s, 找到 %d 个角色", user.Username, len(user.Roles))
	
	// 检查角色，查找管理员角色
	for _, role := range user.Roles {
		log.Printf("IsAdmin: 用户角色: ID=%d, 名称=%s, Slug=%s", role.ID, role.Name, role.Slug)
		
		// 检查角色名称或标识是否包含管理员关键字
		roleName := strings.ToLower(role.Name)
		roleSlug := strings.ToLower(role.Slug)
		
		if strings.Contains(roleName, "admin") || 
		   strings.Contains(roleName, "administrator") || 
		   strings.Contains(roleSlug, "admin") ||
		   role.ID == 1 { // 角色ID=1通常是管理员角色
			log.Printf("IsAdmin: 用户ID=%d 被确认为管理员，匹配角色: %s (ID=%d)", userID, role.Name, role.ID)
			return true
		}
	}
	
	// 检查是否具有所有菜单访问权限的角色
	var adminRoleCount int64
	if err := database.DB.Raw(`
		SELECT COUNT(*) FROM admin_roles r
		JOIN admin_role_users ru ON r.id = ru.role_id
		WHERE ru.user_id = ? AND r.all_menu_access = 1
	`, userID).Count(&adminRoleCount).Error; err == nil && adminRoleCount > 0 {
		log.Printf("IsAdmin: 用户ID=%d 被确认为管理员，具有all_menu_access=1的角色", userID)
		return true
	}
	
	log.Printf("IsAdmin: 用户ID=%d 不是管理员", userID)
	return false
}

// RequireAdmin 中间件，确保用户具有管理员权限
func RequireAdmin(c *fiber.Ctx) error {
	if !IsAdmin(c) {
		return Forbidden(c, "需要管理员权限", nil)
	}
	return c.Next()
}

// GetCurrentUserFromContext 从Fiber Context获取当前用户信息
func GetCurrentUserFromContext(c *fiber.Ctx) (models.AdminUser, error) {
	var user models.AdminUser
	
	// 获取用户ID
	userID := GetUserIDFromContext(c)
	if userID == 0 {
		return user, fiber.NewError(fiber.StatusUnauthorized, "未找到有效的用户信息")
	}
	
	// 从数据库查询用户信息
	err := database.DB.First(&user, userID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return user, fiber.NewError(fiber.StatusUnauthorized, "用户不存在")
		}
		return user, err
	}
	
	return user, nil
}