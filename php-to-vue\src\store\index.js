import Vue from 'vue'
import Vuex from 'vuex'
import { login, logout, verifyToken } from '@/api/auth'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user')) || null,
    tokenChecked: false, // 标记是否已经检查过token有效性
    notifications: [], // 存储通知消息
    unreadCount: 0 // 未读通知数量
  },
  getters: {
    isLoggedIn: state => !!state.token,
    hasTokenValidated: state => state.tokenChecked,
    getNotifications: state => state.notifications,
    getUnreadCount: state => state.unreadCount
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
    },
    SET_USER(state, user) {
      state.user = user
    },
    CLEAR_AUTH(state) {
      state.token = ''
      state.user = null
    },
    SET_TOKEN_CHECKED(state, value) {
      state.tokenChecked = value
    },
    // 添加通知相关的mutations
    ADD_NOTIFICATION(state, notification) {
      // 添加通知到列表头部
      state.notifications.unshift({
        ...notification,
        read: false, // 标记为未读
        timestamp: new Date().getTime()
      })
      // 更新未读数量
      state.unreadCount++
    },
    MARK_NOTIFICATION_READ(state, notificationId) {
      const notification = state.notifications.find(n => n.id === notificationId)
      if (notification && !notification.read) {
        notification.read = true
        state.unreadCount = Math.max(0, state.unreadCount - 1)
      }
    },
    MARK_ALL_READ(state) {
      state.notifications.forEach(notification => {
        notification.read = true
      })
      state.unreadCount = 0
    },
    CLEAR_NOTIFICATIONS(state) {
      state.notifications = []
      state.unreadCount = 0
    }
  },
  actions: {
    // 验证令牌有效性
    checkToken({ commit, state }) {
      // 如果没有token，直接标记为已检查并返回
      if (!state.token) {
        commit('SET_TOKEN_CHECKED', true)
        return Promise.resolve(false)
      }

      return verifyToken()
        .then(response => {
          // 根据响应判断token是否有效
          const isValid = response.valid !== false

          if (!isValid) {
            // 如果token无效，清除认证状态
            commit('CLEAR_AUTH')
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            localStorage.removeItem('remember_me')
          }

          commit('SET_TOKEN_CHECKED', true)
          return isValid
        })
        .catch(() => {
          // 发生错误时，假设token无效
          commit('CLEAR_AUTH')
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          localStorage.removeItem('remember_me')
          commit('SET_TOKEN_CHECKED', true)
          return false
        })
    },

    // 登录
    login({ commit }, loginData) {
      return new Promise((resolve, reject) => {
        login(loginData)
          .then(response => {
            // 检查是否需要设置2FA
            if (response.requires_2fa_setup) {
              // 2FA设置流程，不保存正式token，直接返回响应让组件处理
              resolve(response)
              return
            }

            const { token, user_info } = response

            // 将token存储到localStorage和状态中
            localStorage.setItem('token', token)
            commit('SET_TOKEN', token)

            // 保存用户信息，包括角色和 2FA 状态
            const user = user_info || {
              username: loginData.username,
              role: 1 // 临时设置为管理员，实际应该由后端返回
            }

            localStorage.setItem('user', JSON.stringify(user))
            commit('SET_USER', user)

            // 如果选择了记住我，保存设置（让路由守卫可以使用）
            if (loginData.remember === 1) {
              localStorage.setItem('remember_me', '1')
            }

            // 标记token已检查
            commit('SET_TOKEN_CHECKED', true)

            resolve(response)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 登出
    logout({ commit }) {
      return new Promise((resolve) => {
        logout()
          .then(() => {
            // 清除token和用户信息
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            localStorage.removeItem('remember_me')
            commit('CLEAR_AUTH')
            commit('SET_TOKEN_CHECKED', true)
            resolve()
          })
          .catch(() => {
            // 即使后端登出失败，也清除本地认证状态
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            localStorage.removeItem('remember_me')
            commit('CLEAR_AUTH')
            commit('SET_TOKEN_CHECKED', true)
            resolve()
          })
      })
    },

    // 处理WebSocket通知
    handleNotification({ commit, state }, notification) {
      console.log('Vuex收到通知:', notification)
      
      // 检查是否已存在相同ID的通知，避免重复
      const existingNotification = state.notifications.find(n => n.id === notification.id)
      if (existingNotification) {
        console.log('该通知已存在，跳过处理:', notification.id)
        return
      }
      
      // 根据通知类型处理
      switch (notification.type) {
        case 'article':
          console.log('添加公告通知到store')
          commit('ADD_NOTIFICATION', notification)
          
          // 可以使用Element UI的通知组件显示消息
          Vue.prototype.$notify({
            title: notification.title,
            message: notification.content,
            type: 'info',
            duration: 5000,
            position: 'top-right',
            onClick: () => {
              // 点击通知时标记为已读
              commit('MARK_NOTIFICATION_READ', notification.id)
            }
          })
          
          // 打印当前通知状态
          console.log('通知添加后状态:', {
            total: state.notifications.length,
            unread: state.unreadCount
          })
          break
        // 可以添加其他类型的通知处理
        default:
          console.log('未知通知类型:', notification.type)
      }
    },

    // 标记通知为已读
    markAsRead({ commit }, notificationId) {
      commit('MARK_NOTIFICATION_READ', notificationId)
    },

    // 标记所有通知为已读
    markAllAsRead({ commit }) {
      commit('MARK_ALL_READ')
    },

    // 清除所有通知
    clearNotifications({ commit }) {
      commit('CLEAR_NOTIFICATIONS')
    }
  },
  modules: {
    // 这里可以添加其他模块
  }
}) 