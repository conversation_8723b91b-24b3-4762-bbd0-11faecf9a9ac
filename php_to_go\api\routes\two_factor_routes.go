package routes

import (
	"go-fiber-api/api/handlers"
	"go-fiber-api/api/middleware"

	"github.com/gofiber/fiber/v2"
)

// SetupTwoFactorRoutes 设置双因素认证相关路由
func SetupTwoFactorRoutes(router fiber.Router) {
	// 2FA 路由组基础路径
	twoFactor := router.Group("/2fa")

	// 支持临时token的路由（用于首次设置2FA）
	// 生成 2FA 密钥和二维码 - 支持临时token
	twoFactor.Post("/generate", middleware.TempTokenMiddleware(), handlers.GenerateTwoFactorSecret)

	// 验证并启用 2FA - 支持临时token
	twoFactor.Post("/verify", middleware.TempTokenMiddleware(), handlers.VerifyTwoFactorSetup)

	// 需要正式认证的路由
	protectedTwoFactor := twoFactor.Group("/", middleware.AuthMiddleware())

	// 获取 2FA 状态
	protectedTwoFactor.Get("/status", handlers.GetTwoFactorStatus)

	// 禁用 2FA
	protectedTwoFactor.Post("/disable", handlers.DisableTwoFactor)

	// 临时测试端点：快速启用 2FA（仅用于开发测试）
	protectedTwoFactor.Post("/quick-enable", handlers.QuickEnableTwoFactor)

	// 临时测试端点：重置用户2FA状态（仅用于开发测试）
	twoFactor.Post("/reset", handlers.ResetTwoFactor)
}
