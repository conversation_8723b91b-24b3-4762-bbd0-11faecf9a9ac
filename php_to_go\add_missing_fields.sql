-- 添加缺失的字段到数据库表

-- 1. 为admin_roles表添加all_menu_access字段
ALTER TABLE `admin_roles` 
ADD COLUMN `all_menu_access` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否可访问所有菜单' AFTER `remark`;

-- 2. 检查并添加2FA字段到admin_users表（如果不存在）
-- 这些字段可能已经存在，如果执行时报错可以忽略
ALTER TABLE `admin_users` 
ADD COLUMN `two_factor_enabled` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用双因素认证' AFTER `state`;

ALTER TABLE `admin_users` 
ADD COLUMN `two_factor_secret` VARCHAR(255) NULL DEFAULT NULL COMMENT 'TOTP密钥' AFTER `two_factor_enabled`;

ALTER TABLE `admin_users` 
ADD COLUMN `two_factor_setup_at` DATETIME(3) NULL DEFAULT NULL COMMENT '2FA设置时间' AFTER `two_factor_secret`;

-- 3. 为新字段添加索引
CREATE INDEX `idx_admin_roles_all_menu_access` ON `admin_roles` (`all_menu_access`);
CREATE INDEX `idx_admin_users_two_factor_enabled` ON `admin_users` (`two_factor_enabled`);

-- 4. 确保admin用户有正确的角色关联
-- 检查admin用户是否有角色，如果没有则添加默认角色
INSERT IGNORE INTO `admin_role_users` (`role_id`, `user_id`) 
SELECT 1, 1 
WHERE NOT EXISTS (
    SELECT 1 FROM `admin_role_users` WHERE `user_id` = 1
);

-- 5. 确保存在基本角色（如果不存在）
INSERT IGNORE INTO `admin_roles` (`id`, `name`, `slug`, `guard_name`, `remark`, `all_menu_access`, `created_at`, `updated_at`) 
VALUES 
(1, '超级管理员', 'super-admin', 'web', '拥有所有权限的超级管理员', 1, NOW(), NOW()),
(2, '普通用户', 'user', 'web', '普通用户角色', 0, NOW(), NOW());
