<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2FA测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, button {
            padding: 8px;
            margin: 5px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .qr-code {
            margin: 20px 0;
            text-align: center;
        }
        .qr-code img {
            border: 1px solid #ddd;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>2FA功能测试</h1>
    
    <!-- 创建邀请码 -->
    <div class="section">
        <h2>1. 创建邀请码（使用admin账户）</h2>
        <div class="form-group">
            <label>管理员用户名:</label>
            <input type="text" id="adminUsername" value="admin">
        </div>
        <div class="form-group">
            <label>管理员密码:</label>
            <input type="password" id="adminPassword" value="123456">
        </div>
        <button onclick="createInvitationCode()">创建邀请码</button>
        <div id="invitationResult" class="result"></div>
    </div>

    <!-- 注册新用户 -->
    <div class="section">
        <h2>2. 注册新用户</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="newUsername" value="test2fa">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="newPassword" value="123456">
        </div>
        <div class="form-group">
            <label>邮箱:</label>
            <input type="email" id="newEmail" value="<EMAIL>">
        </div>
        <div class="form-group">
            <label>邀请码:</label>
            <input type="text" id="invitationCode" placeholder="从上面获取邀请码">
        </div>
        <button onclick="registerUser()">注册用户</button>
        <div id="registerResult" class="result"></div>
    </div>

    <!-- 登录测试 -->
    <div class="section">
        <h2>3. 新用户首次登录测试</h2>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="username" value="test2fa">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="password" value="123456">
        </div>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>

    <!-- 生成2FA密钥测试 -->
    <div class="section">
        <h2>4. 生成2FA密钥</h2>
        <button onclick="generate2FA()">生成2FA密钥</button>
        <div id="qrResult" class="result"></div>
        <div id="qrCodeContainer" class="qr-code"></div>
    </div>

    <!-- 验证2FA测试 -->
    <div class="section">
        <h2>5. 验证2FA</h2>
        <div class="form-group">
            <label>TOTP验证码:</label>
            <input type="text" id="totpCode" placeholder="输入6位验证码">
        </div>
        <button onclick="verify2FA()">验证2FA</button>
        <div id="verifyResult" class="result"></div>
    </div>

    <script>
        let currentToken = '';
        let secretKey = '';
        let adminToken = '';

        async function createInvitationCode() {
            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;
            const resultDiv = document.getElementById('invitationResult');

            try {
                // 首先获取验证码
                const captchaResponse = await fetch('http://127.0.0.1:3000/api/admin/auth/captcha');
                const captchaData = await captchaResponse.json();

                if (captchaData.code !== 0) {
                    throw new Error('获取验证码失败: ' + captchaData.message);
                }

                // 显示验证码图片
                const captchaImg = document.createElement('img');
                captchaImg.src = captchaData.data.image_data;
                captchaImg.style.margin = '10px 0';
                resultDiv.appendChild(captchaImg);

                // 提示用户输入验证码
                const captchaInput = prompt('请输入验证码:');
                if (!captchaInput) {
                    resultDiv.textContent = '用户取消输入验证码';
                    return;
                }

                // 管理员登录
                const loginResponse = await fetch('http://127.0.0.1:3000/api/admin/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        captcha: captchaInput,
                        captcha_key: captchaData.data.captcha_key
                    })
                });

                const loginData = await loginResponse.json();

                if (loginData.code !== 0) {
                    resultDiv.textContent = '管理员登录失败: ' + loginData.message;
                    return;
                }

                adminToken = loginData.data.token;

                // 创建邀请码
                const invitationResponse = await fetch('http://127.0.0.1:3000/api/admin/invitation/index', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + adminToken
                    },
                    body: JSON.stringify({
                        code: 'TEST2FA' + Date.now(),
                        description: '2FA测试邀请码',
                        state: 1,
                        limit: 1,
                        count: 1
                    })
                });

                const invitationData = await invitationResponse.json();

                if (invitationData.code === 0) {
                    const invitationCode = invitationData.data[0].code;
                    document.getElementById('invitationCode').value = invitationCode;
                    resultDiv.textContent = '邀请码创建成功: ' + invitationCode;
                } else {
                    resultDiv.textContent = '创建邀请码失败: ' + invitationData.message;
                }
            } catch (error) {
                resultDiv.textContent = '创建邀请码错误: ' + error.message;
            }
        }

        async function registerUser() {
            const username = document.getElementById('newUsername').value;
            const password = document.getElementById('newPassword').value;
            const email = document.getElementById('newEmail').value;
            const invitationCode = document.getElementById('invitationCode').value;
            const resultDiv = document.getElementById('registerResult');

            try {
                // 获取验证码
                const captchaResponse = await fetch('http://127.0.0.1:3000/api/admin/auth/captcha');
                const captchaData = await captchaResponse.json();

                if (captchaData.code !== 0) {
                    throw new Error('获取验证码失败: ' + captchaData.message);
                }

                // 显示验证码图片
                const captchaImg = document.createElement('img');
                captchaImg.src = captchaData.data.image_data;
                captchaImg.style.margin = '10px 0';
                resultDiv.appendChild(captchaImg);

                // 提示用户输入验证码
                const captchaInput = prompt('请输入验证码:');
                if (!captchaInput) {
                    resultDiv.textContent = '用户取消输入验证码';
                    return;
                }

                // 注册用户
                const registerResponse = await fetch('http://127.0.0.1:3000/api/admin/auth/signup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        email: email,
                        invitation_code: invitationCode,
                        captcha: captchaInput,
                        captcha_key: captchaData.data.captcha_key
                    })
                });

                const registerData = await registerResponse.json();

                if (registerData.code === 0) {
                    resultDiv.textContent = '用户注册成功! 用户ID: ' + registerData.data.id;
                } else {
                    resultDiv.textContent = '用户注册失败: ' + registerData.message;
                }
            } catch (error) {
                resultDiv.textContent = '注册用户错误: ' + error.message;
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');

            try {
                // 首先获取验证码
                const captchaResponse = await fetch('http://127.0.0.1:3000/api/admin/auth/captcha');
                const captchaData = await captchaResponse.json();
                
                if (captchaData.code !== 0) {
                    throw new Error('获取验证码失败: ' + captchaData.message);
                }

                // 显示验证码图片
                const captchaImg = document.createElement('img');
                captchaImg.src = captchaData.data.image_data;
                captchaImg.style.margin = '10px 0';
                resultDiv.appendChild(captchaImg);

                // 提示用户输入验证码
                const captchaInput = prompt('请输入验证码:');
                if (!captchaInput) {
                    resultDiv.textContent = '用户取消输入验证码';
                    return;
                }

                // 登录请求
                const loginResponse = await fetch('http://127.0.0.1:3000/api/admin/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        captcha: captchaInput,
                        captcha_key: captchaData.data.captcha_key
                    })
                });

                const loginData = await loginResponse.json();

                if (loginData.code === 0) {
                    // 检查是否需要设置2FA
                    if (loginData.data.requires_2fa_setup) {
                        currentToken = loginData.data.temp_token;
                        resultDiv.textContent = '首次登录成功，需要设置2FA!\n临时Token: ' + currentToken + '\n请继续设置2FA';
                    } else {
                        currentToken = loginData.data.token;
                        resultDiv.textContent = '登录成功!\nToken: ' + currentToken;
                    }
                } else {
                    resultDiv.textContent = '登录失败: ' + loginData.message;
                }
            } catch (error) {
                resultDiv.textContent = '登录错误: ' + error.message;
            }
        }

        async function generate2FA() {
            const resultDiv = document.getElementById('qrResult');
            const qrContainer = document.getElementById('qrCodeContainer');

            if (!currentToken) {
                resultDiv.textContent = '请先登录获取token';
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:3000/api/admin/auth/2fa/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + currentToken
                    }
                });

                const data = await response.json();
                
                if (data.code === 0) {
                    secretKey = data.data.secret;
                    resultDiv.textContent = '2FA密钥生成成功!\n密钥: ' + secretKey + '\n二维码URL: ' + data.data.qr_code_url;
                    
                    // 显示二维码
                    if (data.data.qr_code_url) {
                        const qrImg = document.createElement('img');
                        qrImg.src = data.data.qr_code_url;
                        qrImg.alt = 'Google Authenticator 二维码';
                        qrContainer.innerHTML = '';
                        qrContainer.appendChild(qrImg);
                        
                        const hint = document.createElement('p');
                        hint.textContent = '请使用 Google Authenticator 扫描此二维码';
                        qrContainer.appendChild(hint);
                    }
                } else {
                    resultDiv.textContent = '生成2FA密钥失败: ' + data.message;
                }
            } catch (error) {
                resultDiv.textContent = '生成2FA密钥错误: ' + error.message;
            }
        }

        async function verify2FA() {
            const totpCode = document.getElementById('totpCode').value;
            const resultDiv = document.getElementById('verifyResult');

            if (!currentToken) {
                resultDiv.textContent = '请先登录获取token';
                return;
            }

            if (!totpCode) {
                resultDiv.textContent = '请输入TOTP验证码';
                return;
            }

            try {
                const response = await fetch('http://127.0.0.1:3000/api/admin/auth/2fa/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer ' + currentToken
                    },
                    body: JSON.stringify({
                        code: totpCode
                    })
                });

                const data = await response.json();
                
                if (data.code === 0) {
                    resultDiv.textContent = '2FA验证成功!\n响应: ' + JSON.stringify(data.data, null, 2);
                } else {
                    resultDiv.textContent = '2FA验证失败: ' + data.message;
                }
            } catch (error) {
                resultDiv.textContent = '2FA验证错误: ' + error.message;
            }
        }
    </script>
</body>
</html>
