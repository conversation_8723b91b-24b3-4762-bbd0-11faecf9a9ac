# SQL性能优化方案

## 🚨 问题概述

根据日志分析，系统存在严重的SQL性能问题：

1. **models/menu.go:33** - 大量重复的 `SELECT * FROM admin_menu WHERE parent_id = X` 查询，每个查询耗时约 300ms
2. **api/handlers/project_handler.go:72** - `SELECT count(*) FROM projects` 耗时 7.8 秒
3. **api/handlers/project_handler.go:79** - `SELECT * FROM projects ORDER BY id DESC LIMIT 10` 耗时 308ms
4. **api/handlers/project_handler.go:86** - 多个 `SELECT count(*) FROM projects_content WHERE project_id = X` 查询，每个耗时约 300ms
5. **api/handlers/menu_handler.go:333** - `SELECT * FROM admin_menu ORDER BY parent_id ASC, order ASC` 查询耗时 15 秒

## ✅ 优化方案

### 1. 修复Menu模型的N+1查询问题

**问题**: `AfterFind` 钩子导致每个菜单项都执行额外的子菜单查询

**解决方案**:
- 移除 `AfterFind` 钩子
- 实现 `GetMenuTreeFromDB` 方法，一次性查询所有菜单并在内存中构建树形结构
- 添加 `LoadChildrenBatch` 方法支持批量加载子菜单

**文件**: `models/menu.go`

### 2. 优化项目列表查询性能

**问题**: 为每个项目单独执行内容统计查询

**解决方案**:
- 使用批量查询替代N+1查询
- 实现项目内容统计缓存机制
- 一次性查询所有项目的内容统计

**文件**: `api/handlers/project_handler.go`

### 3. 实现菜单缓存策略

**问题**: 每次请求都重新查询菜单数据

**解决方案**:
- 实现 `MenuCacheService` 缓存服务
- 使用Redis缓存菜单树数据
- 在菜单变更时自动清除缓存

**文件**: `utils/menu_cache.go`, `api/handlers/menu_handler.go`

### 4. 实现项目统计缓存

**问题**: 项目内容统计查询频繁且耗时

**解决方案**:
- 实现 `ProjectCacheService` 缓存服务
- 批量获取和缓存项目内容统计
- 在内容变更时自动清除相关缓存

**文件**: `utils/project_cache.go`, `api/handlers/project_content_handler.go`

### 5. 添加数据库索引

**问题**: 关键查询字段缺少索引

**解决方案**:
- 为 `admin_menu.parent_id` 添加索引
- 为 `projects.user_id`, `projects.state` 添加索引
- 为 `projects_content.project_id` 添加索引
- 添加复合索引优化排序查询

**文件**: `database/migrations/add_performance_indexes.sql`, `cmd/migrate/add_indexes.go`

## 🚀 部署步骤

### 1. 创建数据库索引

```bash
# 方法1: 使用Go脚本自动创建
cd cmd/migrate
go run add_indexes.go

# 方法2: 手动执行SQL脚本
mysql -u username -p database_name < database/migrations/add_performance_indexes.sql
```

### 2. 重启应用

重启应用以加载新的代码优化：

```bash
# 停止当前应用
pkill -f your-app-name

# 重新编译和启动
go build -o your-app-name cmd/server/main.go
./your-app-name
```

### 3. 验证优化效果

```bash
# 运行性能测试
cd cmd/performance_test
go run main.go
```

## 📊 预期性能提升

### 菜单查询优化
- **N+1查询消除**: 从 O(n) 次查询减少到 1 次查询
- **缓存命中**: 后续请求响应时间从 300ms 降低到 <10ms
- **内存优化**: 减少数据库连接占用

### 项目列表查询优化
- **批量统计**: 从 N 次 count 查询减少到 1 次 GROUP BY 查询
- **缓存加速**: 统计数据缓存15分钟，大幅减少数据库负载
- **响应时间**: 项目列表加载时间预计减少 80%

### 数据库索引优化
- **查询加速**: 关键查询响应时间预计提升 5-10 倍
- **排序优化**: ORDER BY 查询性能显著提升
- **并发支持**: 减少锁等待时间

## 🔧 配置说明

### Redis缓存配置

确保 `.env` 文件中配置了Redis连接信息：

```env
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0
```

### 缓存过期时间

- 菜单缓存: 30分钟
- 项目统计缓存: 15分钟

可在相应的缓存服务文件中调整：

```go
// utils/menu_cache.go
const menuCacheExpiration = 30 * time.Minute

// utils/project_cache.go  
const projectContentCountExpiration = 15 * time.Minute
```

## 🔍 监控和调试

### 查看缓存状态

```bash
# 连接Redis查看缓存键
redis-cli
> KEYS menu_cache:*
> KEYS project_content_count:*
```

### 查看数据库索引

```sql
-- 查看表索引
SHOW INDEX FROM admin_menu;
SHOW INDEX FROM projects;
SHOW INDEX FROM projects_content;
```

### 性能监控

应用启动后会在日志中显示缓存命中情况：

```
菜单缓存命中: 从Redis获取到 25 个菜单
项目内容统计缓存部分命中: 3/10 个项目需要查询数据库
```

## ⚠️ 注意事项

1. **缓存一致性**: 在菜单或项目内容变更时，系统会自动清除相关缓存
2. **内存使用**: Redis缓存会占用一定内存，建议监控Redis内存使用情况
3. **索引维护**: 新增的索引会略微影响写入性能，但大幅提升查询性能
4. **回滚方案**: 如遇问题，可删除新增索引并重启应用回到原始状态

## 🧪 测试验证

运行性能测试脚本验证优化效果：

```bash
cd cmd/performance_test
go run main.go
```

测试将输出各种查询方式的性能对比，包括：
- 传统N+1查询 vs 优化查询 vs 缓存查询
- 缓存命中率和响应时间对比
- 数据库查询次数统计

## 📈 持续优化建议

1. **监控慢查询**: 定期检查MySQL慢查询日志
2. **缓存预热**: 在应用启动时预热常用缓存
3. **索引优化**: 根据实际查询模式调整索引策略
4. **分页优化**: 对大数据量列表实现更高效的分页机制
