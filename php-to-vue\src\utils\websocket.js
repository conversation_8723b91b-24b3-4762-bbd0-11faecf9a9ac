import store from '@/store'

class WebSocketService {
  constructor() {
    this.socket = null
    this.connected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000 // 5秒
  }

  // 连接WebSocket
  connect() {
    // 检查用户是否登录
    if (!store.getters.isLoggedIn) {
      console.log('用户未登录，不连接WebSocket')
      return
    }

    // 确定WebSocket地址
    let wsUrl = ''
    
    // 优先使用环境变量中的WebSocket地址
    if (process.env.VUE_APP_WS_API) {
      // 直接使用环境变量中配置的完整WebSocket地址
      wsUrl = `http://127.0.0.1:3000/ws/connect`
      console.log('使用环境变量中的WebSocket地址:', wsUrl)
    } else {
      // 如果没有环境变量，则根据当前环境构建WebSocket地址
      const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:'
      
      if (process.env.NODE_ENV === 'production') {
        // 生产环境使用相对路径，通过Nginx代理
        wsUrl = `http://127.0.0.1:3000/ws/connect`
      } else {
        // 开发环境直接连接到后端
        wsUrl = `http://127.0.0.1:3000/ws/connect`
      }
    }
    
    console.log('尝试连接WebSocket:', wsUrl)
    
    // 如果已经连接，先关闭之前的连接
    if (this.socket) {
      this.socket.close()
    }
    
    this.socket = new WebSocket(wsUrl)
    
    this.socket.onopen = this.onOpen.bind(this)
    this.socket.onmessage = this.onMessage.bind(this)
    this.socket.onclose = this.onClose.bind(this)
    this.socket.onerror = this.onError.bind(this)
  }

  // 连接成功
  onOpen() {
    this.connected = true
    this.reconnectAttempts = 0
    console.log('WebSocket连接成功')
    
    // 可以在连接成功后发送认证信息
    this.sendAuth()
  }

  // 发送认证信息
  sendAuth() {
    const token = store.state.token
    if (token) {
      this.send({
        type: 'auth',
        token
      })
    }
  }

  // 发送消息
  send(data) {
    if (this.connected && this.socket) {
      this.socket.send(JSON.stringify(data))
    } else {
      console.error('WebSocket未连接，无法发送消息')
    }
  }

  // 接收消息
  onMessage(event) {
    try {
      const data = JSON.parse(event.data)
      console.log('收到WebSocket消息:', data)
      
      // 处理不同类型的消息
      if (data && data.type) {
        // 系统消息处理
        if (data.type === 'system') {
          console.log('收到系统消息:', data.title, data.content)
          // 可以显示一个提示
          if (window.Vue && window.Vue.prototype) {
            window.Vue.prototype.$message({
              message: data.content || data.title,
              type: 'success',
              duration: 3000
            })
          }
          return
        }
        
        // 认证消息处理
        if (data.type === 'auth') {
          console.log('收到认证消息，认证成功')
          return
        }
        
        // 通知消息处理 - 需要有id和title
        if (data.type === 'article' && data.id && data.title) {
          // 确保content字段存在，如果不存在则提供默认值
          if (!data.content) {
            data.content = data.title
          }
          
          console.log('收到文章通知:', data.title)
          
          // 分发到Vuex store处理
          store.dispatch('handleNotification', data)
          
          // 通过全局事件总线触发事件，通知组件
          if (window.Vue && window.Vue.prototype) {
            const eventBus = window.Vue.prototype.$root
            if (eventBus) {
              console.log('触发new-notification事件')
              eventBus.$emit('new-notification', data)
            }
          }
          
          // 如果浏览器支持，使用原生通知API
          this.showBrowserNotification(data)
          return
        }
        
        // 其他类型的消息
        console.log('收到其他类型消息:', data.type, data)
      } else {
        console.warn('WebSocket消息格式不正确，缺少type字段:', data)
      }
    } catch (e) {
      console.error('解析WebSocket消息失败:', e)
    }
  }
  
  // 显示浏览器原生通知
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const options = {
        body: notification.content,
        icon: '/favicon.ico',
        tag: `notification-${notification.id}`
      }
      
      const notif = new Notification(notification.title, options)
      
      notif.onclick = function() {
        window.focus()
        // 可以添加导航到通知详情的逻辑
        if (notification.type === 'article') {
          window.location.href = `/#/article/detail/${notification.id}`
        }
      }
    } 
    // 请求通知权限
    else if ('Notification' in window && Notification.permission !== 'denied') {
      Notification.requestPermission()
    }
  }

  // 连接关闭
  onClose() {
    this.connected = false
    console.log('WebSocket连接关闭')
    
    // 尝试重连
    this.reconnect()
  }

  // 连接错误
  onError(error) {
    console.error('WebSocket错误:', error)
    this.connected = false
  }

  // 重连
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`WebSocket尝试第${this.reconnectAttempts}次重连...`)
      
      setTimeout(() => {
        this.connect()
      }, this.reconnectInterval)
    } else {
      console.error('WebSocket重连次数超过最大限制，停止重连')
    }
  }

  // 关闭连接
  close() {
    if (this.socket) {
      this.socket.close()
      this.socket = null
      this.connected = false
    }
  }
}

// 创建单例
const webSocketService = new WebSocketService()

export default webSocketService