# 使用本地Vue代码构建前端
FROM node:18-alpine AS frontend-builder

# 设置工作目录
WORKDIR /build

# 配置npm镜像源
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY ../../php-to-vue/package*.json ./

# 安装依赖
RUN npm install

# 复制源代码
COPY ../../php-to-vue/ ./

# 创建生产环境变量
RUN echo "VUE_APP_BASE_API=/api" > .env.production && \
    echo "VUE_APP_WS_API=ws://localhost:88/ws" >> .env.production && \
    echo "NODE_ENV=production" >> .env.production

# 构建前端项目
RUN npm run build

# Nginx生产环境
FROM nginx:alpine

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装必要工具
RUN apk add --no-cache curl

# 删除默认配置
RUN rm /etc/nginx/conf.d/default.conf

# 复制nginx配置
COPY nginx.prod.conf /etc/nginx/conf.d/default.conf

# 从前端构建阶段复制构建文件
COPY --from=frontend-builder /build/dist /usr/share/nginx/html

# 创建日志目录
RUN mkdir -p /var/log/nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html
RUN chown -R nginx:nginx /var/log/nginx

# 创建SSL证书目录
RUN mkdir -p /etc/nginx/ssl

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
