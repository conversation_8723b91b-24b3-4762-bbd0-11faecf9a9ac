# 使用本地Go代码构建后端服务
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /build

# 设置Go模块代理和环境变量
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装构建依赖
RUN apk add --no-cache git ca-certificates tzdata

# 复制Go模块文件
COPY ../../php_to_go/go.mod ../../php_to_go/go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY ../../php_to_go/ ./

# 构建API服务器
RUN go build -ldflags="-w -s" -o server ./cmd/server/main.go

# 构建Worker服务
RUN go build -ldflags="-w -s" -o worker ./cmd/worker/main.go

# 生产环境运行阶段
FROM alpine:latest

# 配置Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装运行时依赖
RUN apk add --no-cache ca-certificates tzdata wget curl

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /build/server /app/server
COPY --from=builder /build/worker /app/worker

# 复制静态资源文件
COPY --from=builder /build/assets /app/assets
COPY --from=builder /build/uploads /app/uploads

# 复制配置文件
COPY --from=builder /build/config /app/config

# 创建必要的目录
RUN mkdir -p /app/uploads /app/logs

# 设置文件权限
RUN chown -R appuser:appgroup /app
RUN chmod +x /app/server /app/worker

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 3000 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/health || exit 1

# 默认启动API服务器
CMD ["/app/server"]
