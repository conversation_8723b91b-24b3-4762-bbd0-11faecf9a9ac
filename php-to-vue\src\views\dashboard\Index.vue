<template>
  <div class="dashboard">
    <h1 class="dashboard-title">XSS测试平台 - 控制面板</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="6" v-for="(card, index) in statCards" :key="index">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <i :class="card.icon"></i>
            </div>
            <div class="stat-data">
              <div class="stat-value">{{ card.value }}</div>
              <div class="stat-title">{{ card.title }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能区域 -->
    <el-card shadow="hover" class="feature-section">
      <div slot="header">
        <span>功能模块</span>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(feature, index) in features" :key="index">
          <el-card 
            shadow="hover" 
            class="feature-card"
            @click.native="navigateTo(feature.route)"
          >
            <i :class="feature.icon"></i>
            <div class="feature-title">{{ feature.title }}</div>
            <div class="feature-desc">{{ feature.description }}</div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 管理员功能 -->
    <el-card v-if="isAdmin" shadow="hover" class="feature-section">
      <div slot="header">
        <span>管理员功能</span>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="(feature, index) in adminFeatures" :key="index">
          <el-card 
            shadow="hover" 
            class="feature-card admin-feature-card"
            @click.native="navigateTo(feature.route)"
          >
            <i :class="feature.icon"></i>
            <div class="feature-title">{{ feature.title }}</div>
            <div class="feature-desc">{{ feature.description }}</div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近活动 -->
    <el-card shadow="hover" class="activity-section">
      <div slot="header">
        <span>最近活动</span>
      </div>
      <el-table :data="recentLogs" stripe style="width: 100%" v-loading="logsLoading">
        <el-table-column prop="created_at" label="时间" width="180"></el-table-column>
        <el-table-column prop="method" label="类型" width="100"></el-table-column>
        <el-table-column prop="path" label="路径" width="180"></el-table-column>
        <el-table-column prop="username" label="用户"></el-table-column>
        <el-table-column prop="ip" label="IP地址" width="120"></el-table-column>
      </el-table>
    </el-card>

    <!-- 系统信息 -->
    <el-card shadow="hover" class="system-info-section" v-if="isAdmin && systemInfo.version">
      <div slot="header">
        <span>系统信息</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="系统版本">{{ systemInfo.version }}</el-descriptions-item>
        <el-descriptions-item label="运行时间">{{ systemInfo.uptime }}</el-descriptions-item>
        <el-descriptions-item label="Go版本">{{ systemInfo.go_version }}</el-descriptions-item>
        <el-descriptions-item label="服务器时间">{{ systemInfo.server_time }}</el-descriptions-item>
        <el-descriptions-item label="数据库版本">{{ systemInfo.database_version }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script>
import { getDashboardData, getDashboardLogs, getSystemInfo } from '@/api/dashboard'

export default {
  name: 'Dashboard',
  data() {
    return {
      // 统计卡片数据
      statCards: [
        { title: '用户总数', value: '0', icon: 'el-icon-user' },
        { title: '角色总数', value: '0', icon: 'el-icon-user-solid' },
        { title: '权限总数', value: '0', icon: 'el-icon-lock' },
        { title: '项目总数', value: '0', icon: 'el-icon-folder' }
      ],
      // 功能模块 - 移除了不存在的功能
      features: [
        { 
          title: '项目管理', 
          description: '创建和管理XSS钓鱼项目', 
          icon: 'el-icon-folder', 
          route: '/project' 
        },
        { 
          title: '模块管理', 
          description: '创建和配置XSS钓鱼模块', 
          icon: 'el-icon-s-grid', 
          route: '/module' 
        },
        { 
          title: '域名管理', 
          description: '添加和管理域名过滤', 
          icon: 'el-icon-link', 
          route: '/domain' 
        },
        { 
          title: '公告管理', 
          description: '管理系统公告和通知', 
          icon: 'el-icon-document', 
          route: '/article/list' 
        },
        { 
          title: '友情链接', 
          description: '管理友情链接', 
          icon: 'el-icon-connection', 
          route: '/friendly/list' 
        },
        { 
          title: '邀请码管理', 
          description: '管理系统邀请码', 
          icon: 'el-icon-key', 
          route: '/invitation' 
        },
        { 
          title: '系统设置', 
          description: '管理系统配置和参数', 
          icon: 'el-icon-setting', 
          route: '/settings' 
        },
        { 
          title: '用户信息', 
          description: '管理个人账户信息', 
          icon: 'el-icon-user', 
          route: '/user/profile' 
        }
      ],
      // 最近活动
      activities: [
        { time: '2023-05-05 10:23', type: '项目', content: '创建了新项目"测试项目1"', status: '成功' },
        { time: '2023-05-04 16:45', type: '模块', content: '创建了新模块"模块1"', status: '完成' },
        { time: '2023-05-04 09:12', type: '模块', content: '更新了XSS钓鱼模块代码', status: '成功' },
        { time: '2023-05-03 14:30', type: '域名', content: '添加了新的域名过滤规则', status: '成功' },
        { time: '2023-05-02 11:05', type: '任务', content: '完成了系统配置更新', status: '成功' }
      ],
      // 管理员功能模块 - 更新为正确路径
      adminFeatures: [
        { 
          title: '用户管理', 
          description: '管理平台用户', 
          icon: 'el-icon-s-custom', 
          route: '/admin/user/index' 
        },
        { 
          title: '角色管理', 
          description: '管理用户角色', 
          icon: 'el-icon-user-solid', 
          route: '/admin/role/index' 
        },
        { 
          title: '权限管理', 
          description: '管理系统权限', 
          icon: 'el-icon-lock', 
          route: '/admin/permission/index' 
        },
        { 
          title: '菜单管理', 
          description: '管理系统菜单', 
          icon: 'el-icon-menu', 
          route: '/menu/list' 
        }
      ],
      // 控制面板数据
      dashboardData: {},
      // 系统信息
      systemInfo: {},
      // 最近日志
      recentLogs: [],
      // 加载状态
      loading: false,
      logsLoading: false
    }
  },
  computed: {
    // 判断当前用户是否为管理员
    isAdmin() {
      try {
        const userStr = localStorage.getItem('user')
        if (userStr) {
          const user = JSON.parse(userStr)
          return user && (user.role === 1 || user.is_admin === true)
        }
        return false
      } catch (error) {
        return false
      }
    }
  },
  methods: {
    // 获取状态标签类型
    getStatusType(status) {
      const map = {
        '成功': 'success',
        '完成': 'success',
        '进行中': 'warning',
        '失败': 'danger',
        '等待中': 'info'
      }
      return map[status] || 'info'
    },
    
    // 跳转到指定路由
    navigateTo(route) {
      this.$router.push(route)
    },
    
    // 获取仪表盘数据
    async fetchDashboardData() {
      this.loading = true
      try {
        const data = await getDashboardData()
        this.dashboardData = data
        
        // 更新统计卡片数据
        this.statCards[0].value = data.user_count.toString()
        this.statCards[1].value = data.role_count.toString()
        this.statCards[2].value = data.permission_count.toString()
        this.statCards[3].value = data.project_count.toString()
        
        console.log('控制面板数据获取成功:', data)
      } catch (error) {
        console.error('获取控制面板数据失败:', error)
        this.$message.error('获取控制面板数据失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取系统信息
    async fetchSystemInfo() {
      try {
        const data = await getSystemInfo()
        this.systemInfo = data
        console.log('系统信息获取成功:', data)
      } catch (error) {
        console.error('获取系统信息失败:', error)
      }
    },
    
    // 获取最近日志
    async fetchRecentLogs() {
      this.logsLoading = true
      try {
        const data = await getDashboardLogs(10)
        // 确保data是数组，如果不是则使用空数组
        this.recentLogs = Array.isArray(data) ? data : []
        console.log('最近日志获取成功:', data, '类型:', typeof data, '是否为数组:', Array.isArray(data))
      } catch (error) {
        console.error('获取最近日志失败:', error)
        this.recentLogs = [] // 出错时设置为空数组
      } finally {
        this.logsLoading = false
      }
    }
  },
  mounted() {
    this.fetchDashboardData()
    this.fetchRecentLogs()
    if (this.isAdmin) {
      this.fetchSystemInfo()
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.dashboard-title {
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.chart-row {
  margin-top: 20px;
}

.stat-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
}

.stat-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 20px;
}

.stat-icon {
  margin-right: 20px;
}

.stat-icon i {
  font-size: 48px;
}

.stat-data {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  line-height: 1.2;
  margin-bottom: 5px;
}

.stat-title {
  font-size: 14px;
  color: #606266;
}

/* 设置不同卡片的图标颜色 */
.stat-card:nth-child(1) .stat-icon i {
  color: #409EFF;
}

.stat-card:nth-child(2) .stat-icon i {
  color: #67C23A;
}

.stat-card:nth-child(3) .stat-icon i {
  color: #E6A23C;
}

.stat-card:nth-child(4) .stat-icon i {
  color: #F56C6C;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-chart {
  color: #909399;
  font-size: 14px;
}

.feature-section, .activity-section, .system-info-section {
  margin-top: 20px;
}

.feature-card {
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.feature-card i {
  font-size: 36px;
  color: #409EFF;
  margin-bottom: 10px;
}

.feature-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.feature-desc {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.admin-feature-card {
  background-color: #f0f9ff;
  border: 1px solid #d0e8ff;
}

.admin-feature-card i {
  color: #1890ff;
}
</style>