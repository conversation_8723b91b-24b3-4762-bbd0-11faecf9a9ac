-- 性能优化索引脚本
-- 执行前请备份数据库
-- 注意：如果索引已存在，会报错但不影响功能，可以忽略重复索引的错误

-- 1. admin_menu 表索引优化
-- 为 parent_id 添加索引，优化菜单树查询
CREATE INDEX idx_admin_menu_parent_id ON admin_menu(parent_id);

-- 为 parent_id + order 添加复合索引，优化排序查询
CREATE INDEX idx_admin_menu_parent_order ON admin_menu(parent_id, `order`);

-- 为 title 添加索引，优化标题搜索
CREATE INDEX idx_admin_menu_title ON admin_menu(title);

-- 2. projects 表索引优化
-- 为 user_id 添加索引，优化用户项目查询
CREATE INDEX idx_projects_user_id ON projects(user_id);

-- 为 state 添加索引，优化状态过滤
CREATE INDEX idx_projects_state ON projects(state);

-- 为 title 添加索引，优化标题搜索
CREATE INDEX idx_projects_title ON projects(title);

-- 为 unique_key 添加索引，优化唯一性检查
CREATE INDEX idx_projects_unique_key ON projects(unique_key);

-- 为 user_id + state 添加复合索引，优化用户状态过滤
CREATE INDEX idx_projects_user_state ON projects(user_id, state);

-- 为 created_at 添加索引，优化时间排序
CREATE INDEX idx_projects_created_at ON projects(created_at);

-- 3. projects_content 表索引优化
-- 为 project_id 添加索引，优化项目内容查询
CREATE INDEX idx_projects_content_project_id ON projects_content(project_id);

-- 为 user_id 添加索引，优化用户内容查询
CREATE INDEX idx_projects_content_user_id ON projects_content(user_id);

-- 为 project_id + user_id 添加复合索引，优化用户项目内容查询
CREATE INDEX idx_projects_content_project_user ON projects_content(project_id, user_id);

-- 为 cid 添加索引（如果不是主键的话）
-- CREATE INDEX idx_projects_content_cid ON projects_content(cid);

-- 4. admin_role_menu 表索引优化
-- 为 role_id 添加索引，优化角色菜单查询
CREATE INDEX idx_admin_role_menu_role_id ON admin_role_menu(role_id);

-- 为 menu_id 添加索引，优化菜单角色查询
CREATE INDEX idx_admin_role_menu_menu_id ON admin_role_menu(menu_id);

-- 5. admin_role_users 表索引优化
-- 为 admin_user_id 添加索引，优化用户角色查询
CREATE INDEX idx_admin_role_users_user_id ON admin_role_users(admin_user_id);

-- 为 role_id 添加索引，优化角色用户查询
CREATE INDEX idx_admin_role_users_role_id ON admin_role_users(role_id);

-- 6. admin_roles 表索引优化
-- 为 name 添加索引，优化角色名称查询
CREATE INDEX idx_admin_roles_name ON admin_roles(name);

-- 为 all_menu_access 添加索引，优化权限查询
CREATE INDEX idx_admin_roles_all_menu_access ON admin_roles(all_menu_access);

-- 7. admin_permissions 表索引优化（如果存在）
-- 为 parent_id 添加索引，优化权限树查询
CREATE INDEX idx_admin_permissions_parent_id ON admin_permissions(parent_id);

-- 为 parent_id + order 添加复合索引，优化排序查询
CREATE INDEX idx_admin_permissions_parent_order ON admin_permissions(parent_id, `order`);

-- 为 slug 添加索引，优化权限标识查询
CREATE INDEX idx_admin_permissions_slug ON admin_permissions(slug);

-- 8. admin_users 表索引优化
-- 为 username 添加索引，优化用户名查询
CREATE INDEX idx_admin_users_username ON admin_users(username);

-- 为 email 添加索引，优化邮箱查询
CREATE INDEX idx_admin_users_email ON admin_users(email);

-- 为 status 添加索引，优化状态过滤
CREATE INDEX idx_admin_users_status ON admin_users(status);

-- 9. 删除时间索引优化（软删除）
-- 为 deleted_at 添加索引，优化软删除查询
CREATE INDEX idx_projects_deleted_at ON projects(deleted_at);
CREATE INDEX idx_projects_content_deleted_at ON projects_content(deleted_at);

-- 显示索引创建完成信息
SELECT 'Performance indexes created successfully!' as message;

-- 查看表的索引信息（可选）
-- SHOW INDEX FROM admin_menu;
-- SHOW INDEX FROM projects;
-- SHOW INDEX FROM projects_content;
-- SHOW INDEX FROM admin_role_menu;
-- SHOW INDEX FROM admin_role_users;
