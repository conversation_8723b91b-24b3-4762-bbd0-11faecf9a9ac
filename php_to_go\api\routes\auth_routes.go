package routes

import (
	"go-fiber-api/api/handlers"
	"strings"
	"log"

	"github.com/gofiber/fiber/v2"
	"go-fiber-api/utils"
)

// SetupAuthRoutes 设置认证相关路由 (公开访问)
func SetupAuthRoutes(router fiber.Router) {
	auth := router.Group("/auth")

	// 公开访问的路由
	auth.Post("/signup", handlers.Signup)
	auth.Post("/login", handlers.Login)
	auth.Get("/captcha", handlers.GetCaptcha)

	auth.Post("/forgot", handlers.ForgotPassword)
	
	// 添加验证令牌的路由
	auth.Get("/verify", handlers.VerifyToken)
	
	// 添加邮箱验证路由
	auth.Get("/verify-email", handlers.VerifyEmail)

	// 添加2FA状态检查路由
	auth.Get("/check-2fa-status", handlers.CheckTwoFactorStatus)
	
	// 添加GET方法的重置密码路由，用于处理链接点击
	auth.Get("/reset", func(c *fiber.Ctx) error {
		// 获取token参数
		token := c.Query("token")
		if token == "" {
			return c.Status(400).JSON(fiber.Map{
				"code":    400,
				"message": "缺少重置令牌",
				"data":    nil,
			})
		}
		
		// 使用模板工具加载重置密码页面模板
		content, err := utils.GetEmailTemplate("password_reset_page")
		if err != nil {
			log.Printf("无法加载密码重置页面模板: %v", err)
			return c.Status(500).JSON(fiber.Map{
				"code":    500,
				"message": "系统错误，请联系管理员",
				"data":    nil,
			})
		}
		
		// 替换模板中的令牌
		htmlContent := strings.ReplaceAll(content, "{{.Token}}", token)
		
		// 返回HTML页面
		return c.Status(200).Type("html").SendString(htmlContent)
	})
	
	// 处理POST请求的重置密码路由
	auth.Post("/reset", handlers.ResetPassword)
	
	// 需要认证的路由
	protectedRoutes := auth.Group("/")
	protectedRoutes.Use(utils.RequireAuthentication)
	
	// 用户资料相关
	protectedRoutes.Get("/profile", handlers.GetProfile)
	protectedRoutes.Put("/profile", handlers.UpdateProfile)
	
	// 头像上传路由 
	protectedRoutes.Post("/upload/avatar", handlers.UploadAvatar)
	
	// 获取当前用户信息和权限
	protectedRoutes.Get("/current-user", handlers.GetCurrentUser)
}
