<!DOCTYPE html>
<html>
<head>
    <title>重置2FA状态</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        button { padding: 10px 20px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>重置2FA状态工具</h1>
    
    <div class="section">
        <h2>重置admin用户的2FA状态</h2>
        <p>这将清除admin用户的2FA设置，使其可以重新设置2FA</p>
        <button onclick="resetAdmin2FA()">重置admin的2FA</button>
        <div id="resetResult" class="result"></div>
    </div>

    <script>
        async function resetAdmin2FA() {
            const resultDiv = document.getElementById('resetResult');

            try {
                // 使用测试端点重置2FA
                const response = await fetch('http://127.0.0.1:3000/api/auth/2fa/reset', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin'
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 0) {
                    resultDiv.textContent = '✅ admin用户的2FA状态已重置，现在可以重新设置2FA了';
                    resultDiv.style.background = '#d4edda';
                    resultDiv.style.color = '#155724';
                } else {
                    resultDiv.textContent = '❌ 重置失败: ' + (data.message || '未知错误');
                    resultDiv.style.background = '#f8d7da';
                    resultDiv.style.color = '#721c24';
                }
            } catch (error) {
                console.error('重置2FA失败:', error);
                resultDiv.textContent = '❌ 重置失败: ' + error.message;
                resultDiv.style.background = '#f8d7da';
                resultDiv.style.color = '#721c24';
            }
        }
    </script>
</body>
</html>
