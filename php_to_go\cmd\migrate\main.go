package main

import (
	"fmt"
	"go-fiber-api/database"
	"log"
)

func main() {
	// 初始化数据库连接
	if err := database.ConnDatabase(); err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 执行2FA字段迁移
	if err := migrate2FAFields(); err != nil {
		log.Fatal("迁移失败:", err)
	}

	fmt.Println("2FA字段迁移完成!")
}

func migrate2FAFields() error {
	db := database.DB
	
	// 检查字段是否已存在
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = 'go_fiber' AND TABLE_NAME = 'admin_users' AND COLUMN_NAME = 'two_factor_enabled'").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("检查字段失败: %v", err)
	}
	
	if count > 0 {
		fmt.Println("2FA字段已存在，跳过迁移")
		return nil
	}
	
	// 添加2FA字段
	sqls := []string{
		"ALTER TABLE `admin_users` ADD COLUMN `two_factor_enabled` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否启用双因素认证' AFTER `state`",
		"ALTER TABLE `admin_users` ADD COLUMN `two_factor_secret` VARCHAR(255) NULL DEFAULT NULL COMMENT 'TOTP密钥' AFTER `two_factor_enabled`",
		"ALTER TABLE `admin_users` ADD COLUMN `two_factor_setup_at` DATETIME(3) NULL DEFAULT NULL COMMENT '2FA设置时间' AFTER `two_factor_secret`",
		"CREATE INDEX `idx_admin_users_two_factor_enabled` ON `admin_users` (`two_factor_enabled`)",
	}
	
	for _, sql := range sqls {
		fmt.Printf("执行SQL: %s\n", sql)
		if err := db.Exec(sql).Error; err != nil {
			return fmt.Errorf("执行SQL失败: %v", err)
		}
	}
	
	return nil
}
