package main

import (
	"fmt"
	"go-fiber-api/database"
	"log"
	"strings"

	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Println("警告: 找不到.env文件")
	}

	// 初始化数据库连接
	if err := database.ConnDatabase(); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	db := database.GetDB()
	
	// 检查invitation表结构
	fmt.Println("检查invitation表结构:")
	rows, err := db.Raw("DESCRIBE invitation").Rows()
	if err != nil {
		log.Fatalf("查询表结构失败: %v", err)
	}
	defer rows.Close()

	fmt.Printf("%-20s %-20s %-10s %-10s %-20s %-10s\n", "Field", "Type", "Null", "Key", "Default", "Extra")
	fmt.Println(strings.Repeat("-", 100))
	
	for rows.Next() {
		var field, fieldType, null, key, defaultValue, extra string
		if err := rows.Scan(&field, &fieldType, &null, &key, &defaultValue, &extra); err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}
		fmt.Printf("%-20s %-20s %-10s %-10s %-20s %-10s\n", field, fieldType, null, key, defaultValue, extra)
	}
	
	// 检查是否存在description字段
	var count int64
	err = db.Raw("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'invitation' AND COLUMN_NAME = 'description'").Scan(&count).Error
	if err != nil {
		log.Fatalf("检查description字段失败: %v", err)
	}
	
	fmt.Printf("\ndescription字段存在: %t\n", count > 0)
	
	if count == 0 {
		fmt.Println("\n需要添加description字段到invitation表")
		fmt.Println("建议执行以下SQL:")
		fmt.Println("ALTER TABLE `invitation` ADD COLUMN `description` VARCHAR(500) DEFAULT '' COMMENT '邀请码描述' AFTER `code`;")
	}
}
