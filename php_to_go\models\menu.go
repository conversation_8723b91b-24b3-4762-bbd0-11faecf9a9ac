package models

import (
	"time"
	"gorm.io/gorm"
)

// Menu 菜单模型
type Menu struct {
	ID        uint64    `json:"id" gorm:"primaryKey;autoIncrement;type:bigint unsigned;comment:菜单ID"`
	ParentID  uint64    `json:"parent_id" gorm:"type:bigint;not null;default:0;comment:父级菜单ID"`
	Order     uint64    `json:"order" gorm:"type:bigint;not null;default:0;comment:排序"`
	Title     string    `json:"title" gorm:"size:50;not null;comment:菜单标题"`
	Icon      string    `json:"icon" gorm:"size:50;comment:菜单图标"`
	URI       string    `json:"uri" gorm:"size:50;comment:菜单URI"`
	CreatedAt time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"comment:更新时间"`

	// 关联关系
	Roles []Role `gorm:"many2many:admin_role_menu;foreignKey:ID;joinForeignKey:MenuID;References:ID;joinReferences:RoleID" json:"roles,omitempty"`
	Children []Menu `gorm:"-" json:"children,omitempty"` // 自引用，用于构建菜单树
}

// TableName 指定表名
func (Menu) TableName() string {
	return "admin_menu"
}

// LoadChildrenBatch 批量加载子菜单，避免N+1查询
func LoadChildrenBatch(db *gorm.DB, menus []Menu) ([]Menu, error) {
    if len(menus) == 0 {
        return menus, nil
    }

    // 收集所有菜单ID
    var menuIDs []uint64
    menuMap := make(map[uint64]*Menu)
    for i := range menus {
        menuIDs = append(menuIDs, menus[i].ID)
        menuMap[menus[i].ID] = &menus[i]
    }

    // 一次性查询所有子菜单
    var allChildren []Menu
    if err := db.Where("parent_id IN ?", menuIDs).Order("parent_id ASC, `order` ASC").Find(&allChildren).Error; err != nil {
        return nil, err
    }

    // 将子菜单分组到对应的父菜单
    for _, child := range allChildren {
        if parent, exists := menuMap[child.ParentID]; exists {
            parent.Children = append(parent.Children, child)
        }
    }

    return menus, nil
}

// GetMenuTreeFromDB 从数据库获取完整的菜单树，优化查询性能
func GetMenuTreeFromDB(db *gorm.DB) ([]Menu, error) {
    var allMenus []Menu
    if err := db.Order("parent_id ASC, `order` ASC").Find(&allMenus).Error; err != nil {
        return nil, err
    }

    // 构建菜单映射
    menuMap := make(map[uint64]*Menu)
    var rootMenus []Menu

    // 第一遍：创建所有菜单的映射
    for i := range allMenus {
        menuMap[allMenus[i].ID] = &allMenus[i]
    }

    // 第二遍：构建父子关系
    for i := range allMenus {
        if allMenus[i].ParentID == 0 {
            // 根菜单
            rootMenus = append(rootMenus, allMenus[i])
        } else {
            // 子菜单，添加到父菜单的Children中
            if parent, exists := menuMap[allMenus[i].ParentID]; exists {
                parent.Children = append(parent.Children, allMenus[i])
            }
        }
    }

    return rootMenus, nil
}