<!DOCTYPE html>
<html>
<head>
    <title>测试重置2FA</title>
</head>
<body>
    <h1>测试重置2FA接口</h1>
    <button onclick="testReset()">测试重置</button>
    <div id="result"></div>

    <script>
        async function testReset() {
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('调用重置接口...');
                const response = await fetch('http://127.0.0.1:3000/api/reset-2fa', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin'
                    })
                });

                console.log('响应状态:', response.status);
                const data = await response.json();
                console.log('响应数据:', data);
                
                resultDiv.innerHTML = `
                    <h3>响应结果:</h3>
                    <p>状态码: ${response.status}</p>
                    <p>响应: ${JSON.stringify(data, null, 2)}</p>
                `;
            } catch (error) {
                console.error('请求失败:', error);
                resultDiv.innerHTML = `<p>错误: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
